<?php
/**
 * User Logout API
 * Handles user logout and session cleanup
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Check if user is logged in
    if (SessionManager::isLoggedIn()) {
        $userID = SessionManager::getUserId();
        
        // Initialize database for logging
        $database = new Database();
        
        // Log logout event
        $database->logAnalytics($userID, 'LOGOUT', 'Authentication', [
            'logout_time' => date('Y-m-d H:i:s'),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    }
    
    // Clear session
    SessionManager::logout();
    
    ApiResponse::success(null, 'Logout successful');
    
} catch (Exception $e) {
    error_log("Logout error: " . $e->getMessage());
    
    // Clear session anyway
    SessionManager::logout();
    
    ApiResponse::success(null, 'Logout completed');
}
?>
