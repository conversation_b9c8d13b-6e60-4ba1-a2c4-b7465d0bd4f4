<?php
/**
 * Database Configuration for ZamShipment Courier Services
 * Handles database connection and common database operations
 */

class Database {
    private $host = "localhost";
    private $db_name = "courier_db";
    private $username = "root";
    private $password = "";
    private $conn;

    /**
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $this->conn->exec("set names utf8");
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed");
        }

        return $this->conn;
    }

    /**
     * Close database connection
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->conn->rollback();
    }

    /**
     * Execute a prepared statement
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $exception) {
            error_log("Query error: " . $exception->getMessage());
            throw new Exception("Database query failed");
        }
    }

    /**
     * Get last inserted ID
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }

    /**
     * Sanitize input data
     */
    public static function sanitize($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitize'], $data);
        }

        return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate email address
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate phone number (basic validation)
     */
    public static function validatePhone($phone) {
        // Remove all non-digit characters except +
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phone);

        // Check if it's a valid length (7-15 digits, optionally starting with +)
        return preg_match('/^\+?[0-9]{7,15}$/', $cleanPhone);
    }

    /**
     * Validate email format
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }

    /**
     * Validate phone number (Zambian format)
     */
    public static function validatePhone($phone) {
        // Remove spaces and special characters
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // Check if it matches Zambian phone format
        return preg_match('/^(\+260|0)?[0-9]{9}$/', $phone);
    }

    /**
     * Generate tracking number
     */
    public static function generateTrackingNumber() {
        $year = date('Y');
        $random = str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        return "PKG-{$year}-{$random}";
    }

    /**
     * Calculate shipping cost based on weight and delivery mode
     */
    public static function calculateShippingCost($weight, $deliveryMode, $shipmentType = 'Standard') {
        $baseCost = 0;
        
        // Base cost by delivery mode
        switch($deliveryMode) {
            case 'Air':
                $baseCost = 15.00;
                break;
            case 'Road':
                $baseCost = 8.00;
                break;
            case 'Rail':
                $baseCost = 6.00;
                break;
            case 'Ocean':
                $baseCost = 12.00;
                break;
            default:
                $baseCost = 10.00;
        }
        
        // Weight multiplier (per kg)
        $weightCost = $weight * 5.00;
        
        // Shipment type multiplier
        $typeMultiplier = 1.0;
        switch($shipmentType) {
            case 'Express':
                $typeMultiplier = 1.5;
                break;
            case 'Special Handling':
                $typeMultiplier = 2.0;
                break;
            default:
                $typeMultiplier = 1.0;
        }
        
        $totalCost = ($baseCost + $weightCost) * $typeMultiplier;
        
        return round($totalCost, 2);
    }

    /**
     * Log error to database
     */
    public function logError($userID, $errorType, $errorMessage, $stackTrace = null, $requestData = null) {
        try {
            $query = "INSERT INTO ErrorLog (UserID, ErrorType, ErrorMessage, StackTrace, RequestURL, RequestMethod, RequestData, IPAddress, UserAgent) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $userID,
                $errorType,
                $errorMessage,
                $stackTrace,
                $_SERVER['REQUEST_URI'] ?? null,
                $_SERVER['REQUEST_METHOD'] ?? null,
                $requestData ? json_encode($requestData) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ];
            
            $this->execute($query, $params);
        } catch(Exception $e) {
            error_log("Failed to log error to database: " . $e->getMessage());
        }
    }

    /**
     * Log analytics event
     */
    public function logAnalytics($userID, $action, $module, $details = null) {
        try {
            $query = "INSERT INTO Analytics (UserID, Action, Module, Details, IPAddress, UserAgent, SessionID) 
                     VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $userID,
                $action,
                $module,
                $details ? json_encode($details) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null,
                session_id()
            ];
            
            $this->execute($query, $params);
        } catch(Exception $e) {
            error_log("Failed to log analytics: " . $e->getMessage());
        }
    }
}

/**
 * Response helper class
 */
class ApiResponse {
    public static function success($data = null, $message = "Success", $code = 200) {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }

    public static function error($message = "Error", $code = 400, $details = null) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }

    public static function unauthorized($message = "Unauthorized access") {
        self::error($message, 401);
    }

    public static function forbidden($message = "Access forbidden") {
        self::error($message, 403);
    }

    public static function notFound($message = "Resource not found") {
        self::error($message, 404);
    }

    public static function serverError($message = "Internal server error") {
        self::error($message, 500);
    }
}

/**
 * Session management helper
 */
class SessionManager {
    public static function start() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public static function isLoggedIn() {
        self::start();
        return isset($_SESSION['user_id']) && isset($_SESSION['username']);
    }

    public static function getUserId() {
        self::start();
        return $_SESSION['user_id'] ?? null;
    }

    public static function getUsername() {
        self::start();
        return $_SESSION['username'] ?? null;
    }

    public static function getUserRole() {
        self::start();
        return $_SESSION['role'] ?? null;
    }

    public static function setUser($userID, $username, $role, $email) {
        self::start();
        $_SESSION['user_id'] = $userID;
        $_SESSION['username'] = $username;
        $_SESSION['role'] = $role;
        $_SESSION['email'] = $email;
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
    }

    public static function logout() {
        self::start();
        session_unset();
        session_destroy();
    }

    public static function checkTimeout($timeoutMinutes = 15) {
        self::start();
        if (isset($_SESSION['last_activity'])) {
            $timeout = $timeoutMinutes * 60; // Convert to seconds
            if (time() - $_SESSION['last_activity'] > $timeout) {
                self::logout();
                return false;
            }
        }
        $_SESSION['last_activity'] = time();
        return true;
    }

    public static function requireLogin() {
        if (!self::isLoggedIn() || !self::checkTimeout()) {
            ApiResponse::unauthorized("Please log in to access this resource");
        }
    }

    public static function requireRole($allowedRoles) {
        self::requireLogin();
        $userRole = self::getUserRole();
        
        if (!in_array($userRole, (array)$allowedRoles)) {
            ApiResponse::forbidden("Insufficient permissions");
        }
    }
}
?>
