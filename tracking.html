<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Track Your Package - ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="Track your package in real-time with ZamSend courier services. Enter your tracking number to get live updates on your shipment status." />
	<meta name="keywords" content="package tracking, shipment status, courier tracking, ZamSend, delivery updates" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">

	<!-- Custom tracking styles -->
	<style>
		.tracking-hero {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 150px 0;
		}
		
		.tracking-form {
			background: white;
			border-radius: 15px;
			padding: 30px;
			box-shadow: 0 10px 30px rgba(0,0,0,0.1);
			margin-top: -50px;
			position: relative;
			z-index: 10;
		}
		
		.tracking-input {
			height: 60px;
			border-radius: 10px;
			border: 2px solid #e1e5e9;
			padding: 0 20px;
			font-size: 18px;
		}
		
		.tracking-input:focus {
			border-color: #667eea;
			box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
		}
		
		.btn-track {
			height: 60px;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 10px;
			color: white;
			font-size: 18px;
			font-weight: 600;
			padding: 0 30px;
		}
		
		.btn-track:hover {
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
		}
		
		.tracking-result {
			display: none;
			margin-top: 40px;
		}
		
		.shipment-card {
			background: white;
			border-radius: 15px;
			padding: 30px;
			box-shadow: 0 5px 20px rgba(0,0,0,0.1);
			margin-bottom: 30px;
		}
		
		.status-badge {
			padding: 8px 16px;
			border-radius: 20px;
			font-weight: 600;
			font-size: 14px;
		}
		
		.status-pending { background: #fff3cd; color: #856404; }
		.status-picked-up { background: #d4edda; color: #155724; }
		.status-in-transit { background: #cce7ff; color: #004085; }
		.status-out-for-delivery { background: #f8d7da; color: #721c24; }
		.status-delivered { background: #d1ecf1; color: #0c5460; }
		.status-cancelled { background: #f5c6cb; color: #721c24; }
		
		.tracking-timeline {
			position: relative;
			padding-left: 30px;
		}
		
		.tracking-timeline::before {
			content: '';
			position: absolute;
			left: 15px;
			top: 0;
			bottom: 0;
			width: 2px;
			background: #e9ecef;
		}
		
		.timeline-item {
			position: relative;
			margin-bottom: 30px;
		}
		
		.timeline-item::before {
			content: '';
			position: absolute;
			left: -23px;
			top: 5px;
			width: 12px;
			height: 12px;
			border-radius: 50%;
			background: #667eea;
			border: 3px solid white;
			box-shadow: 0 0 0 3px #667eea;
		}
		
		.timeline-item.completed::before {
			background: #28a745;
			box-shadow: 0 0 0 3px #28a745;
		}
		
		.timeline-content {
			background: #f8f9fa;
			padding: 15px;
			border-radius: 8px;
		}
		
		.timeline-date {
			font-size: 12px;
			color: #6c757d;
			margin-bottom: 5px;
		}
		
		.timeline-title {
			font-weight: 600;
			margin-bottom: 5px;
		}
		
		.timeline-description {
			font-size: 14px;
			color: #6c757d;
			margin: 0;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<nav class="fh5co-nav" role="navigation">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html">Home</a></li>
							<li><a href="services.html">Services</a></li>
							<li class="active"><a href="tracking.html">Track Package</a></li>
							<li class="has-dropdown">
								<a href="pricing.html">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html">About</a></li>
							<li><a href="contact.html">Contact</a></li>
							<li class="btn-cta"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta"><a href="ship-now.html"><span>Ship Now</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="tracking-hero">
		<div class="container">
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center">
					<h1>Track Your Package</h1>
					<p>Enter your tracking number to get real-time updates on your shipment</p>
				</div>
			</div>
		</div>
	</div>

	<div class="container">
		<div class="row">
			<div class="col-md-8 col-md-offset-2">
				<div class="tracking-form">
					<form id="trackingForm">
						<div class="row">
							<div class="col-md-8">
								<input type="text" class="form-control tracking-input" id="trackingNumber" 
								       placeholder="Enter tracking number (e.g., PKG-2024-001234)" required>
							</div>
							<div class="col-md-4">
								<button type="submit" class="btn btn-track btn-block">
									<i class="icon-search"></i> Track Package
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
		
		<div class="row">
			<div class="col-md-10 col-md-offset-1">
				<div id="trackingResult" class="tracking-result">
					<!-- Tracking results will be loaded here -->
				</div>
			</div>
		</div>
	</div>

	<footer id="fh5co-footer" role="contentinfo" style="margin-top: 100px;">
		<div class="container">
			<div class="row copyright">
				<div class="col-md-12 text-center">
					<p>
						<small class="block">&copy; 2025 ZamSend Courier Services. All Rights Reserved.</small> 
					</p>
				</div>
			</div>
		</div>
	</footer>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	<!-- Main -->
	<script src="js/main.js"></script>
	
	<script>
		$(document).ready(function() {
			$('#trackingForm').on('submit', function(e) {
				e.preventDefault();
				
				const trackingNumber = $('#trackingNumber').val().trim();
				if (!trackingNumber) {
					alert('Please enter a tracking number');
					return;
				}
				
				// Show loading state
				$('#trackingResult').html('<div class="text-center"><i class="icon-spinner icon-spin"></i> Searching for your package...</div>').show();

				// Make API call to track package
				$.ajax({
					url: 'api/tracking/track.php',
					method: 'POST',
					data: JSON.stringify({ tracking_number: trackingNumber }),
					contentType: 'application/json',
					success: function(response) {
						if (response.success) {
							displayTrackingResult(response.data);
						} else {
							$('#trackingResult').html('<div class="alert alert-danger">Error: ' + response.message + '</div>');
						}
					},
					error: function(xhr) {
						let errorMessage = 'An error occurred while tracking your package.';
						try {
							const response = JSON.parse(xhr.responseText);
							errorMessage = response.message || errorMessage;
						} catch (e) {
							// Use default error message
						}
						$('#trackingResult').html('<div class="alert alert-danger">' + errorMessage + '</div>');
					}
				});
			});
			
			function displayTrackingResult(data) {
				const shipment = data.shipment;
				const sender = data.sender;
				const recipient = data.recipient;
				const tracking = data.tracking;

				const statusClass = 'status-' + shipment.status.toLowerCase().replace(/\s+/g, '-');

				let eventsHtml = '';
				tracking.events.forEach(function(event) {
					const completedClass = event.completed ? 'completed' : '';
					const eventDate = event.datetime || event.date || '';
					const eventTitle = event.type || event.title || '';
					const eventDesc = event.description || '';
					const eventLocation = event.location ? ` - ${event.location}` : '';

					eventsHtml += `
						<div class="timeline-item ${completedClass}">
							<div class="timeline-content">
								<div class="timeline-date">${eventDate}</div>
								<div class="timeline-title">${eventTitle}${eventLocation}</div>
								<p class="timeline-description">${eventDesc}</p>
							</div>
						</div>
					`;
				});

				// Progress bar
				const progressHtml = `
					<div class="progress" style="height: 8px; margin-bottom: 20px;">
						<div class="progress-bar" role="progressbar" style="width: ${tracking.progress_percentage}%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
					</div>
				`;

				const resultHtml = `
					<div class="shipment-card">
						<div class="row">
							<div class="col-md-8">
								<h3>Tracking Number: ${shipment.tracking_number}</h3>
								<p><strong>Status:</strong> <span class="status-badge ${statusClass}">${shipment.status}</span></p>
								<p><strong>Priority:</strong> ${shipment.priority} | <strong>Type:</strong> ${shipment.shipment_type}</p>
							</div>
							<div class="col-md-4 text-right">
								<p><strong>Estimated Delivery:</strong><br>${shipment.estimated_delivery_date || 'TBD'}</p>
								<p><strong>Weight:</strong> ${shipment.weight} kg</p>
							</div>
						</div>
						${progressHtml}
						<hr>
						<div class="row">
							<div class="col-md-6">
								<h5>Sender</h5>
								<p><strong>${sender.name}</strong><br>${sender.address}<br>Phone: ${sender.phone}</p>
							</div>
							<div class="col-md-6">
								<h5>Recipient</h5>
								<p><strong>${recipient.name}</strong><br>${recipient.address}<br>Phone: ${recipient.phone}</p>
							</div>
						</div>
						${shipment.special_instructions ? `<div class="alert alert-info"><strong>Special Instructions:</strong> ${shipment.special_instructions}</div>` : ''}
					</div>

					<div class="shipment-card">
						<h4>Tracking History</h4>
						<div class="tracking-timeline">
							${eventsHtml}
						</div>
					</div>
				`;

				$('#trackingResult').html(resultHtml);
			}
		});
	</script>

	</body>
</html>
