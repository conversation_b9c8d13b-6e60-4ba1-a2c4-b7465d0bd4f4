<?php
/**
 * Resend Email Verification API
 * Handles resending verification emails for unverified accounts
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST; // Fallback to form data
    }
    
    // Can resend by token or email
    $token = Database::sanitize($input['token'] ?? '');
    $email = Database::sanitize($input['email'] ?? '');
    
    if (empty($token) && empty($email)) {
        ApiResponse::error('Verification token or email address is required');
    }
    
    // Initialize database
    $database = new Database();
    
    // Find user by token or email
    if (!empty($token)) {
        $userQuery = "SELECT UserID, Email, EmailVerified, FirstName, LastName 
                      FROM Users 
                      WHERE EmailVerificationToken = ?";
        $userStmt = $database->execute($userQuery, [$token]);
    } else {
        $userQuery = "SELECT UserID, Email, EmailVerified, FirstName, LastName 
                      FROM Users 
                      WHERE Email = ? AND EmailVerified = 0";
        $userStmt = $database->execute($userQuery, [$email]);
    }
    
    $user = $userStmt->fetch();
    
    if (!$user) {
        ApiResponse::error('User not found or already verified', 404);
    }
    
    // Check if already verified
    if ($user['EmailVerified']) {
        ApiResponse::error('Email address is already verified', 400);
    }
    
    // Check rate limiting (prevent spam)
    $rateLimitQuery = "SELECT COUNT(*) as count 
                       FROM Analytics 
                       WHERE UserID = ? 
                       AND Action = 'RESEND_VERIFICATION' 
                       AND CreatedAt > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    
    $rateLimitStmt = $database->execute($rateLimitQuery, [$user['UserID']]);
    $rateLimitResult = $rateLimitStmt->fetch();
    
    if ($rateLimitResult['count'] >= 3) {
        ApiResponse::error('Too many verification emails sent. Please wait before requesting another.', 429);
    }
    
    // Generate new verification token
    $newToken = bin2hex(random_bytes(32));
    
    // Begin transaction
    $database->beginTransaction();
    
    try {
        // Update verification token
        $updateQuery = "UPDATE Users 
                       SET EmailVerificationToken = ?,
                           UpdatedAt = NOW()
                       WHERE UserID = ?";
        
        $database->execute($updateQuery, [$newToken, $user['UserID']]);
        
        // Create notification
        $notificationQuery = "INSERT INTO Notifications (UserID, Type, Title, Message, Priority)
                             VALUES (?, 'System', 'Verification Email Sent', ?, 'Medium')";
        
        $message = "A new verification email has been sent to your email address. Please check your inbox and click the verification link.";
        $database->execute($notificationQuery, [$user['UserID'], $message]);
        
        // Log analytics
        $database->logAnalytics($user['UserID'], 'RESEND_VERIFICATION', 'Auth', [
            'email' => $user['Email'],
            'method' => !empty($token) ? 'token' : 'email'
        ]);
        
        // Commit transaction
        $database->commit();
        
        // Send verification email (simulated)
        $verificationLink = "http://localhost/ZamShipment/verify-email.html?token=" . $newToken;
        
        // In a real application, you would send an actual email here
        // For demo purposes, we'll just log it
        error_log("Verification email would be sent to: {$user['Email']} with link: $verificationLink");
        
        // Prepare response data
        $responseData = [
            'email' => $user['Email'],
            'verification_link' => $verificationLink, // Only for demo
            'sent_at' => date('Y-m-d H:i:s')
        ];
        
        ApiResponse::success($responseData, 'Verification email sent successfully. Please check your inbox.');
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Resend verification error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            null,
            'RESEND_VERIFICATION_ERROR',
            $e->getMessage(),
            $e->getTraceAsString(),
            $input ?? []
        );
    }
    
    ApiResponse::serverError('An error occurred while sending verification email. Please try again.');
}
?>
