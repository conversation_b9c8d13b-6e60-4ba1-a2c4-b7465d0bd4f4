<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Shipping Calculator &mdash; ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="Calculate shipping costs for international courier services with ZamSend. Get instant quotes for express, standard, and economy shipping." />
	<meta name="keywords" content="shipping calculator, courier cost, international shipping rates, ZamSend calculator" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">

	<!-- Custom calculator styles -->
	<style>
		.calc-hero {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 80px 0;
		}
		
		.calc-container {
			background: white;
			border-radius: 15px;
			padding: 40px;
			box-shadow: 0 10px 30px rgba(0,0,0,0.1);
			margin-top: -50px;
			position: relative;
			z-index: 10;
		}
		
		.calc-form {
			background: #f8f9fa;
			padding: 30px;
			border-radius: 10px;
			margin-bottom: 30px;
		}
		
		.form-control {
			height: 45px;
			border-radius: 8px;
			border: 2px solid #e1e5e9;
			padding: 0 15px;
			margin-bottom: 15px;
		}
		
		.form-control:focus {
			border-color: #667eea;
			box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
		}
		
		.btn-calculate {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 25px;
			color: white;
			font-size: 16px;
			font-weight: 600;
			padding: 12px 30px;
			width: 100%;
		}
		
		.btn-calculate:hover {
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
		}
		
		.results-container {
			display: none;
		}
		
		.service-result {
			background: white;
			border: 2px solid #e9ecef;
			border-radius: 10px;
			padding: 25px;
			margin-bottom: 20px;
			transition: all 0.3s ease;
		}
		
		.service-result:hover {
			border-color: #667eea;
			transform: translateY(-2px);
		}
		
		.service-result.recommended {
			border-color: #28a745;
			background: #f8fff9;
		}
		
		.service-name {
			font-size: 20px;
			font-weight: 700;
			color: #333;
			margin-bottom: 10px;
		}
		
		.service-price {
			font-size: 28px;
			font-weight: 700;
			color: #667eea;
			margin-bottom: 10px;
		}
		
		.service-time {
			color: #6c757d;
			margin-bottom: 15px;
		}
		
		.service-features {
			list-style: none;
			padding: 0;
			margin: 15px 0;
		}
		
		.service-features li {
			padding: 3px 0;
			color: #555;
			font-size: 14px;
		}
		
		.service-features li:before {
			content: "✓";
			color: #28a745;
			font-weight: bold;
			margin-right: 8px;
		}
		
		.recommended-badge {
			background: #28a745;
			color: white;
			padding: 5px 15px;
			border-radius: 15px;
			font-size: 12px;
			font-weight: 600;
			float: right;
		}
		
		.zone-info {
			background: #e3f2fd;
			border-left: 4px solid #2196f3;
			padding: 15px;
			margin: 20px 0;
			border-radius: 5px;
		}
		
		.zone-info h5 {
			color: #1976d2;
			margin-bottom: 10px;
		}
		
		.additional-costs {
			background: #fff3cd;
			border-left: 4px solid #ffc107;
			padding: 15px;
			margin: 20px 0;
			border-radius: 5px;
		}
		
		.additional-costs h5 {
			color: #856404;
			margin-bottom: 10px;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<nav class="fh5co-nav" role="navigation">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html">Home</a></li>
							<li><a href="services.html">Services</a></li>
							<li><a href="tracking.html">Track Package</a></li>
							<li class="has-dropdown active">
								<a href="pricing.html">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html">About</a></li>
							<li><a href="contact.html">Contact</a></li>
							<li class="btn-cta"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta"><a href="ship-now.html"><span>Ship Now</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="calc-hero">
		<div class="container">
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center">
					<h1>Shipping Calculator</h1>
					<p>Get instant quotes for your international shipments</p>
				</div>
			</div>
		</div>
	</div>

	<div class="container">
		<div class="row">
			<div class="col-md-8 col-md-offset-2">
				<div class="calc-container">
					<h3>Calculate Shipping Cost</h3>
					<p>Enter your package details to get accurate shipping quotes for all our services.</p>
					
					<div class="calc-form">
						<form id="calculatorForm">
							<div class="row">
								<div class="col-md-6">
									<label>From (Origin)</label>
									<select class="form-control" name="origin" required>
										<option value="">Select Origin</option>
										<option value="Lusaka">Lusaka, Zambia</option>
										<option value="Kitwe">Kitwe, Zambia</option>
										<option value="Ndola">Ndola, Zambia</option>
										<option value="Kabwe">Kabwe, Zambia</option>
										<option value="Livingstone">Livingstone, Zambia</option>
									</select>
								</div>
								<div class="col-md-6">
									<label>To (Destination)</label>
									<select class="form-control" name="destination" required>
										<option value="">Select Destination</option>
										<option value="South Africa">South Africa</option>
										<option value="Kenya">Kenya</option>
										<option value="Tanzania">Tanzania</option>
										<option value="Uganda">Uganda</option>
										<option value="Zimbabwe">Zimbabwe</option>
										<option value="Botswana">Botswana</option>
										<option value="Namibia">Namibia</option>
										<option value="United Kingdom">United Kingdom</option>
										<option value="United States">United States</option>
										<option value="Canada">Canada</option>
										<option value="Australia">Australia</option>
										<option value="China">China</option>
										<option value="India">India</option>
									</select>
								</div>
							</div>
							
							<div class="row">
								<div class="col-md-4">
									<label>Weight (kg)</label>
									<input type="number" class="form-control" name="weight" placeholder="0.0" step="0.1" min="0.1" max="50" required>
								</div>
								<div class="col-md-4">
									<label>Length (cm)</label>
									<input type="number" class="form-control" name="length" placeholder="0" min="1" max="200">
								</div>
								<div class="col-md-4">
									<label>Width (cm)</label>
									<input type="number" class="form-control" name="width" placeholder="0" min="1" max="200">
								</div>
							</div>
							
							<div class="row">
								<div class="col-md-4">
									<label>Height (cm)</label>
									<input type="number" class="form-control" name="height" placeholder="0" min="1" max="200">
								</div>
								<div class="col-md-4">
									<label>Declared Value ($)</label>
									<input type="number" class="form-control" name="value" placeholder="0.00" step="0.01" min="0">
								</div>
								<div class="col-md-4">
									<label>Package Type</label>
									<select class="form-control" name="package_type">
										<option value="regular">Regular Package</option>
										<option value="fragile">Fragile Item</option>
										<option value="documents">Documents</option>
										<option value="electronics">Electronics</option>
									</select>
								</div>
							</div>
							
							<div style="margin-top: 20px;">
								<button type="submit" class="btn btn-calculate">
									<i class="icon-calculator"></i> Calculate Shipping Cost
								</button>
							</div>
						</form>
					</div>
					
					<div id="resultsContainer" class="results-container">
						<h4>Shipping Options & Costs</h4>
						<div id="serviceResults"></div>
						
						<div class="zone-info">
							<h5><i class="icon-info"></i> Delivery Zone Information</h5>
							<p id="zoneInfo">Zone information will be displayed here based on your destination.</p>
						</div>
						
						<div class="additional-costs">
							<h5><i class="icon-warning"></i> Additional Costs</h5>
							<ul>
								<li>Insurance: Optional (2% of declared value, minimum $5)</li>
								<li>Customs clearance: May apply for international shipments</li>
								<li>Remote area surcharge: May apply for certain destinations</li>
								<li>Fuel surcharge: Currently 8% of base rate</li>
							</ul>
						</div>
						
						<div style="margin-top: 30px; text-align: center;">
							<a href="ship-now.html" class="btn btn-calculate" style="width: auto; padding: 15px 40px;">
								<i class="icon-plane"></i> Create Shipment
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<footer id="fh5co-footer" role="contentinfo" style="margin-top: 100px;">
		<div class="container">
			<div class="row copyright">
				<div class="col-md-12 text-center">
					<p>
						<small class="block">&copy; 2024 ZamSend Courier Services. All Rights Reserved.</small> 
					</p>
				</div>
			</div>
		</div>
	</footer>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	<!-- Main -->
	<script src="js/main.js"></script>
	
	<script>
		$(document).ready(function() {
			$('#calculatorForm').on('submit', function(e) {
				e.preventDefault();
				calculateShipping();
			});
		});
		
		function calculateShipping() {
			const formData = new FormData($('#calculatorForm')[0]);
			const data = {};
			
			// Convert FormData to object
			for (let [key, value] of formData.entries()) {
				data[key] = value;
			}
			
			// Validate required fields
			if (!data.origin || !data.destination || !data.weight) {
				alert('Please fill in all required fields');
				return;
			}
			
			const weight = parseFloat(data.weight);
			const destination = data.destination;
			
			// Calculate costs for different services
			const services = calculateServiceCosts(weight, destination, data);
			
			// Display results
			displayResults(services, destination);
		}
		
		function calculateServiceCosts(weight, destination, packageData) {
			// Base rates per kg
			const baseRates = {
				economy: 10,
				standard: 15,
				express: 25,
				special: 50
			};
			
			// Zone multipliers
			const zoneMultipliers = {
				'South Africa': 1.0,
				'Kenya': 1.2,
				'Tanzania': 1.2,
				'Uganda': 1.3,
				'Zimbabwe': 1.1,
				'Botswana': 1.1,
				'Namibia': 1.2,
				'United Kingdom': 2.0,
				'United States': 2.2,
				'Canada': 2.1,
				'Australia': 2.5,
				'China': 1.8,
				'India': 1.7
			};
			
			const multiplier = zoneMultipliers[destination] || 1.5;
			
			// Package type surcharge
			const packageSurcharge = {
				'regular': 0,
				'fragile': 5,
				'documents': -2,
				'electronics': 3
			};
			
			const surcharge = packageSurcharge[packageData.package_type] || 0;
			
			// Calculate costs
			const services = [
				{
					name: 'Economy Shipping',
					code: 'economy',
					price: (baseRates.economy * weight * multiplier) + surcharge,
					time: '10-15 business days',
					features: ['Basic tracking', 'Standard insurance', 'Ocean/Rail transport'],
					recommended: false
				},
				{
					name: 'Standard Shipping',
					code: 'standard',
					price: (baseRates.standard * weight * multiplier) + surcharge,
					time: '5-10 business days',
					features: ['Real-time tracking', 'Full insurance', 'Road/Air transport', 'Customer support'],
					recommended: weight <= 10 && multiplier <= 1.5
				},
				{
					name: 'Express Delivery',
					code: 'express',
					price: (baseRates.express * weight * multiplier) + surcharge,
					time: '2-5 business days',
					features: ['Priority handling', 'Premium insurance', 'Air transport', '24/7 support'],
					recommended: weight <= 5 || multiplier > 1.5
				},
				{
					name: 'Special Handling',
					code: 'special',
					price: (baseRates.special * weight * multiplier) + surcharge,
					time: '1-3 business days',
					features: ['White-glove service', 'Temperature control', 'Custom packaging', 'Dedicated support'],
					recommended: packageData.package_type === 'fragile' || parseFloat(packageData.value || 0) > 500
				}
			];
			
			return services;
		}
		
		function displayResults(services, destination) {
			let resultsHtml = '';
			
			services.forEach(function(service) {
				const recommendedBadge = service.recommended ? '<span class="recommended-badge">Recommended</span>' : '';
				const recommendedClass = service.recommended ? 'recommended' : '';
				
				const featuresHtml = service.features.map(feature => `<li>${feature}</li>`).join('');
				
				resultsHtml += `
					<div class="service-result ${recommendedClass}">
						<div class="service-name">
							${service.name}
							${recommendedBadge}
						</div>
						<div class="service-price">$${service.price.toFixed(2)}</div>
						<div class="service-time"><i class="icon-clock"></i> ${service.time}</div>
						<ul class="service-features">
							${featuresHtml}
						</ul>
						<a href="ship-now.html?service=${service.code}" class="btn btn-primary btn-sm">
							Select ${service.name}
						</a>
					</div>
				`;
			});
			
			$('#serviceResults').html(resultsHtml);
			
			// Update zone info
			const zoneDescriptions = {
				'South Africa': 'Zone 1 - Regional (Southern Africa)',
				'Kenya': 'Zone 2 - Regional (East Africa)',
				'Tanzania': 'Zone 2 - Regional (East Africa)',
				'Uganda': 'Zone 2 - Regional (East Africa)',
				'Zimbabwe': 'Zone 1 - Regional (Southern Africa)',
				'Botswana': 'Zone 1 - Regional (Southern Africa)',
				'Namibia': 'Zone 1 - Regional (Southern Africa)',
				'United Kingdom': 'Zone 4 - International (Europe)',
				'United States': 'Zone 5 - International (North America)',
				'Canada': 'Zone 5 - International (North America)',
				'Australia': 'Zone 6 - International (Oceania)',
				'China': 'Zone 3 - International (Asia)',
				'India': 'Zone 3 - International (Asia)'
			};
			
			$('#zoneInfo').text(zoneDescriptions[destination] || 'Zone information not available for this destination.');
			
			$('#resultsContainer').show();
			
			// Scroll to results
			$('html, body').animate({
				scrollTop: $('#resultsContainer').offset().top - 100
			}, 500);
		}
	</script>

	</body>
</html>
