<?php
/**
 * Email Verification API
 * Handles email address verification for new accounts
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST; // Fallback to form data
    }
    
    // Validate required fields
    if (empty($input['token'])) {
        ApiResponse::error('Verification token is required');
    }
    
    $token = Database::sanitize($input['token']);
    
    // Initialize database
    $database = new Database();
    
    // Find user by verification token
    $userQuery = "SELECT UserID, Email, EmailVerified, IsActive, FirstName, LastName 
                  FROM Users 
                  WHERE EmailVerificationToken = ? AND IsActive = 0";
    
    $userStmt = $database->execute($userQuery, [$token]);
    $user = $userStmt->fetch();
    
    if (!$user) {
        // Check if token exists but user is already verified
        $verifiedQuery = "SELECT UserID, Email, EmailVerified 
                         FROM Users 
                         WHERE EmailVerificationToken = ? AND EmailVerified = 1";
        
        $verifiedStmt = $database->execute($verifiedQuery, [$token]);
        $verifiedUser = $verifiedStmt->fetch();
        
        if ($verifiedUser) {
            ApiResponse::success([
                'already_verified' => true,
                'email' => $verifiedUser['Email']
            ], 'Email address has already been verified');
        }
        
        ApiResponse::error('Invalid or expired verification token', 404);
    }
    
    // Check if already verified
    if ($user['EmailVerified']) {
        ApiResponse::success([
            'already_verified' => true,
            'email' => $user['Email']
        ], 'Email address has already been verified');
    }
    
    // Begin transaction
    $database->beginTransaction();
    
    try {
        // Update user verification status
        $updateQuery = "UPDATE Users 
                       SET EmailVerified = 1, 
                           IsActive = 1, 
                           EmailVerificationToken = NULL,
                           EmailVerifiedAt = NOW()
                       WHERE UserID = ?";
        
        $database->execute($updateQuery, [$user['UserID']]);
        
        // Create welcome notification
        $notificationQuery = "INSERT INTO Notifications (UserID, Type, Title, Message, Priority)
                             VALUES (?, 'Account', 'Account Activated', ?, 'High')";
        
        $welcomeMessage = "Welcome to ZamSend! Your account has been successfully activated. You can now start using our courier services.";
        $database->execute($notificationQuery, [$user['UserID'], $welcomeMessage]);
        
        // Log analytics
        $database->logAnalytics($user['UserID'], 'EMAIL_VERIFICATION', 'Auth', [
            'email' => $user['Email'],
            'verification_method' => 'email_link'
        ]);
        
        // Commit transaction
        $database->commit();
        
        // Prepare response data
        $responseData = [
            'user' => [
                'id' => $user['UserID'],
                'email' => $user['Email'],
                'first_name' => $user['FirstName'],
                'last_name' => $user['LastName'],
                'verified_at' => date('Y-m-d H:i:s')
            ],
            'already_verified' => false
        ];
        
        ApiResponse::success($responseData, 'Email verified successfully. Your account is now active.');
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Email verification error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            null,
            'EMAIL_VERIFICATION_ERROR',
            $e->getMessage(),
            $e->getTraceAsString(),
            $input ?? []
        );
    }
    
    ApiResponse::serverError('An error occurred during email verification. Please try again.');
}
?>
