<?php
/**
 * Admin Dashboard Statistics API
 * Provides comprehensive system statistics for admin dashboard
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Require Super Admin authentication
    SessionManager::requireRole('SuperAdmin');
    $userID = SessionManager::getUserId();
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get total users count
    $usersQuery = "SELECT COUNT(*) as total, 
                   SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as active,
                   SUM(CASE WHEN Role = 'SuperAdmin' THEN 1 ELSE 0 END) as super_admins,
                   SUM(CASE WHEN Role = 'SubAdmin' THEN 1 ELSE 0 END) as sub_admins,
                   SUM(CASE WHEN Role = 'CustomerService' THEN 1 ELSE 0 END) as customer_service,
                   SUM(CASE WHEN Role = 'Logistics' THEN 1 ELSE 0 END) as logistics
                   FROM Users";
    $usersStmt = $database->execute($usersQuery);
    $usersStats = $usersStmt->fetch();
    
    // Get total customers count
    $customersQuery = "SELECT COUNT(*) as total,
                       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as active,
                       COUNT(DISTINCT c.CustomerID) as with_shipments
                       FROM Customer c
                       LEFT JOIN Shipment s ON c.CustomerID = s.CustomerID";
    $customersStmt = $database->execute($customersQuery);
    $customersStats = $customersStmt->fetch();
    
    // Get shipments statistics
    $shipmentsQuery = "SELECT COUNT(*) as total,
                       SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) as pending,
                       SUM(CASE WHEN Status = 'Picked Up' THEN 1 ELSE 0 END) as picked_up,
                       SUM(CASE WHEN Status = 'In Transit' THEN 1 ELSE 0 END) as in_transit,
                       SUM(CASE WHEN Status = 'Out for Delivery' THEN 1 ELSE 0 END) as out_for_delivery,
                       SUM(CASE WHEN Status = 'Delivered' THEN 1 ELSE 0 END) as delivered,
                       SUM(CASE WHEN Status = 'Cancelled' THEN 1 ELSE 0 END) as cancelled,
                       SUM(ShippingCost) as total_revenue,
                       AVG(ShippingCost) as avg_shipping_cost,
                       COUNT(CASE WHEN DATE(CreatedAt) = CURDATE() THEN 1 END) as today_shipments,
                       COUNT(CASE WHEN DATE(CreatedAt) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_shipments,
                       COUNT(CASE WHEN DATE(CreatedAt) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as month_shipments
                       FROM Shipment";
    $shipmentsStmt = $database->execute($shipmentsQuery);
    $shipmentsStats = $shipmentsStmt->fetch();
    
    // Get monthly shipments data for chart
    $monthlyQuery = "SELECT 
                     DATE_FORMAT(CreatedAt, '%Y-%m') as month,
                     COUNT(*) as shipments,
                     SUM(ShippingCost) as revenue
                     FROM Shipment 
                     WHERE CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                     GROUP BY DATE_FORMAT(CreatedAt, '%Y-%m')
                     ORDER BY month";
    $monthlyStmt = $database->execute($monthlyQuery);
    $monthlyData = $monthlyStmt->fetchAll();
    
    // Get delivery performance
    $deliveryQuery = "SELECT 
                      COUNT(*) as total_deliveries,
                      AVG(DATEDIFF(d.DeliveryDate, s.CreatedAt)) as avg_delivery_days,
                      SUM(CASE WHEN d.DeliveryDate <= s.EstimatedDeliveryDate THEN 1 ELSE 0 END) as on_time_deliveries
                      FROM Delivery d
                      JOIN Shipment s ON d.ShipmentID = s.ShipmentID
                      WHERE d.DeliveryStatus = 'Successful'";
    $deliveryStmt = $database->execute($deliveryQuery);
    $deliveryStats = $deliveryStmt->fetch();
    
    // Calculate on-time delivery percentage
    $onTimePercentage = $deliveryStats['total_deliveries'] > 0 
        ? round(($deliveryStats['on_time_deliveries'] / $deliveryStats['total_deliveries']) * 100, 1)
        : 0;
    
    // Get recent activities
    $recentQuery = "SELECT 
                    'shipment' as type,
                    CONCAT('New shipment created: ', TrackingNumber) as message,
                    CreatedAt as timestamp
                    FROM Shipment 
                    WHERE CreatedAt >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    
                    UNION ALL
                    
                    SELECT 
                    'delivery' as type,
                    CONCAT('Package delivered: ', s.TrackingNumber) as message,
                    d.DeliveryDate as timestamp
                    FROM Delivery d
                    JOIN Shipment s ON d.ShipmentID = s.ShipmentID
                    WHERE d.DeliveryDate >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    
                    UNION ALL
                    
                    SELECT 
                    'customer' as type,
                    CONCAT('New customer registered: ', FirstName, ' ', LastName) as message,
                    CreatedAt as timestamp
                    FROM Customer 
                    WHERE CreatedAt >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    
                    ORDER BY timestamp DESC
                    LIMIT 10";
    $recentStmt = $database->execute($recentQuery);
    $recentActivities = $recentStmt->fetchAll();
    
    // Get system alerts (errors from last 24 hours)
    $alertsQuery = "SELECT 
                    ErrorType,
                    ErrorMessage,
                    Severity,
                    COUNT(*) as count,
                    MAX(CreatedAt) as last_occurrence
                    FROM ErrorLog 
                    WHERE CreatedAt >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    AND IsResolved = 0
                    GROUP BY ErrorType, ErrorMessage, Severity
                    ORDER BY Severity DESC, count DESC
                    LIMIT 5";
    $alertsStmt = $database->execute($alertsQuery);
    $systemAlerts = $alertsStmt->fetchAll();
    
    // Get top customers by shipment count
    $topCustomersQuery = "SELECT 
                          c.CustomerID,
                          CONCAT(c.FirstName, ' ', c.LastName) as customer_name,
                          c.Email,
                          COUNT(s.ShipmentID) as shipment_count,
                          SUM(s.ShippingCost) as total_spent
                          FROM Customer c
                          JOIN Shipment s ON c.CustomerID = s.CustomerID
                          GROUP BY c.CustomerID
                          ORDER BY shipment_count DESC
                          LIMIT 5";
    $topCustomersStmt = $database->execute($topCustomersQuery);
    $topCustomers = $topCustomersStmt->fetchAll();
    
    // Format monthly data for charts
    $chartLabels = [];
    $chartShipments = [];
    $chartRevenue = [];
    
    foreach ($monthlyData as $month) {
        $chartLabels[] = date('M Y', strtotime($month['month'] . '-01'));
        $chartShipments[] = (int)$month['shipments'];
        $chartRevenue[] = (float)$month['revenue'];
    }
    
    // Log analytics
    $database->logAnalytics($userID, 'VIEW_ADMIN_DASHBOARD', 'Admin', [
        'total_users' => $usersStats['total'],
        'total_customers' => $customersStats['total'],
        'total_shipments' => $shipmentsStats['total']
    ]);
    
    // Prepare response data
    $responseData = [
        'overview' => [
            'users' => [
                'total' => (int)$usersStats['total'],
                'active' => (int)$usersStats['active'],
                'by_role' => [
                    'super_admins' => (int)$usersStats['super_admins'],
                    'sub_admins' => (int)$usersStats['sub_admins'],
                    'customer_service' => (int)$usersStats['customer_service'],
                    'logistics' => (int)$usersStats['logistics']
                ]
            ],
            'customers' => [
                'total' => (int)$customersStats['total'],
                'active' => (int)$customersStats['active'],
                'with_shipments' => (int)$customersStats['with_shipments']
            ],
            'shipments' => [
                'total' => (int)$shipmentsStats['total'],
                'today' => (int)$shipmentsStats['today_shipments'],
                'this_week' => (int)$shipmentsStats['week_shipments'],
                'this_month' => (int)$shipmentsStats['month_shipments'],
                'by_status' => [
                    'pending' => (int)$shipmentsStats['pending'],
                    'picked_up' => (int)$shipmentsStats['picked_up'],
                    'in_transit' => (int)$shipmentsStats['in_transit'],
                    'out_for_delivery' => (int)$shipmentsStats['out_for_delivery'],
                    'delivered' => (int)$shipmentsStats['delivered'],
                    'cancelled' => (int)$shipmentsStats['cancelled']
                ]
            ],
            'revenue' => [
                'total' => (float)$shipmentsStats['total_revenue'],
                'average_per_shipment' => (float)$shipmentsStats['avg_shipping_cost']
            ],
            'performance' => [
                'total_deliveries' => (int)$deliveryStats['total_deliveries'],
                'average_delivery_days' => (float)$deliveryStats['avg_delivery_days'],
                'on_time_percentage' => $onTimePercentage
            ]
        ],
        'charts' => [
            'monthly_shipments' => [
                'labels' => $chartLabels,
                'shipments' => $chartShipments,
                'revenue' => $chartRevenue
            ],
            'status_distribution' => [
                'labels' => ['Delivered', 'In Transit', 'Pending', 'Out for Delivery', 'Picked Up', 'Cancelled'],
                'data' => [
                    (int)$shipmentsStats['delivered'],
                    (int)$shipmentsStats['in_transit'],
                    (int)$shipmentsStats['pending'],
                    (int)$shipmentsStats['out_for_delivery'],
                    (int)$shipmentsStats['picked_up'],
                    (int)$shipmentsStats['cancelled']
                ]
            ]
        ],
        'recent_activities' => array_map(function($activity) {
            return [
                'type' => $activity['type'],
                'message' => $activity['message'],
                'timestamp' => $activity['timestamp'],
                'time_ago' => timeAgo($activity['timestamp'])
            ];
        }, $recentActivities),
        'system_alerts' => array_map(function($alert) {
            return [
                'type' => $alert['ErrorType'],
                'message' => $alert['ErrorMessage'],
                'severity' => $alert['Severity'],
                'count' => (int)$alert['count'],
                'last_occurrence' => $alert['last_occurrence']
            ];
        }, $systemAlerts),
        'top_customers' => array_map(function($customer) {
            return [
                'id' => (int)$customer['CustomerID'],
                'name' => $customer['customer_name'],
                'email' => $customer['Email'],
                'shipment_count' => (int)$customer['shipment_count'],
                'total_spent' => (float)$customer['total_spent']
            ];
        }, $topCustomers)
    ];
    
    ApiResponse::success($responseData, 'Dashboard statistics retrieved successfully');
    
} catch (Exception $e) {
    error_log("Admin dashboard stats error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            SessionManager::getUserId(),
            'ADMIN_DASHBOARD_ERROR',
            $e->getMessage(),
            $e->getTraceAsString()
        );
    }
    
    ApiResponse::serverError('An error occurred while retrieving dashboard statistics');
}

/**
 * Helper function to calculate time ago
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    
    return date('M j, Y', strtotime($datetime));
}
?>
