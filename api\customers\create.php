<?php
/**
 * Create Customer API
 * Handles creation of new customers
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Require authentication and appropriate permissions
    SessionManager::requireLogin();
    $userID = SessionManager::getUserId();
    $userRole = SessionManager::getUserRole();
    
    // Check permissions (Customer Service, Sub-Admin, or Super Admin can create customers)
    $allowedRoles = ['SuperAdmin', 'SubAdmin', 'CustomerService'];
    if (!in_array($userRole, $allowedRoles)) {
        ApiResponse::forbidden('Insufficient permissions to create customers');
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST; // Fallback to form data
    }
    
    // Validate required fields
    $requiredFields = ['first_name', 'last_name', 'email', 'phone', 'address', 'city', 'state', 'postal_code'];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            ApiResponse::error("Field '$field' is required");
        }
    }
    
    // Sanitize input data
    $firstName = Database::sanitize($input['first_name']);
    $lastName = Database::sanitize($input['last_name']);
    $email = Database::sanitize($input['email']);
    $phone = Database::sanitize($input['phone']);
    $address = Database::sanitize($input['address']);
    $city = Database::sanitize($input['city']);
    $state = Database::sanitize($input['state']);
    $postalCode = Database::sanitize($input['postal_code']);
    $country = Database::sanitize($input['country'] ?? 'Zambia');
    $dateOfBirth = !empty($input['date_of_birth']) ? $input['date_of_birth'] : null;
    
    // Validate data
    if (!Database::validateEmail($email)) {
        ApiResponse::error('Invalid email format');
    }
    
    if (!Database::validatePhone($phone)) {
        ApiResponse::error('Invalid phone number format');
    }
    
    if (strlen($firstName) < 2 || strlen($lastName) < 2) {
        ApiResponse::error('First name and last name must be at least 2 characters long');
    }
    
    if ($dateOfBirth && !strtotime($dateOfBirth)) {
        ApiResponse::error('Invalid date of birth format');
    }
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check if email already exists
    $emailCheckQuery = "SELECT CustomerID FROM Customer WHERE Email = ?";
    $emailStmt = $database->execute($emailCheckQuery, [$email]);
    
    if ($emailStmt->rowCount() > 0) {
        ApiResponse::error('Email address already exists', 409);
    }
    
    // Begin transaction
    $database->beginTransaction();
    
    try {
        // Insert customer
        $customerQuery = "INSERT INTO Customer (
            FirstName, LastName, Email, Phone, Address, City, State, PostalCode, Country, DateOfBirth, IsActive
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";
        
        $customerParams = [
            $firstName, $lastName, $email, $phone, $address, $city, $state, $postalCode, $country, $dateOfBirth
        ];
        
        $database->execute($customerQuery, $customerParams);
        $customerID = $database->lastInsertId();
        
        // Create company record if company details provided
        $companyID = null;
        if (!empty($input['company_name'])) {
            $companyName = Database::sanitize($input['company_name']);
            $registrationNumber = Database::sanitize($input['registration_number'] ?? '');
            $taxID = Database::sanitize($input['tax_id'] ?? '');
            $industry = Database::sanitize($input['industry'] ?? '');
            $website = Database::sanitize($input['website'] ?? '');
            $billingAddress = Database::sanitize($input['billing_address'] ?? $address);
            $contactPerson = Database::sanitize($input['contact_person'] ?? $firstName . ' ' . $lastName);
            $contactEmail = Database::sanitize($input['contact_email'] ?? $email);
            $contactPhone = Database::sanitize($input['contact_phone'] ?? $phone);
            
            $companyQuery = "INSERT INTO Company (
                CustomerID, CompanyName, RegistrationNumber, TaxID, Industry, Website,
                BillingAddress, ContactPerson, ContactEmail, ContactPhone
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $companyParams = [
                $customerID, $companyName, $registrationNumber, $taxID, $industry, $website,
                $billingAddress, $contactPerson, $contactEmail, $contactPhone
            ];
            
            $database->execute($companyQuery, $companyParams);
            $companyID = $database->lastInsertId();
        }
        
        // Create notification for admin
        $notificationQuery = "INSERT INTO Notifications (UserID, Type, Title, Message, Priority)
                             VALUES (?, 'Customer', 'New Customer Registration', ?, 'Medium')";
        
        $notificationMessage = "New customer registered: {$firstName} {$lastName} ({$email})";
        $database->execute($notificationQuery, [$userID, $notificationMessage]);
        
        // Log analytics
        $database->logAnalytics($userID, 'CREATE_CUSTOMER', 'Customers', [
            'customer_id' => $customerID,
            'customer_name' => $firstName . ' ' . $lastName,
            'customer_email' => $email,
            'has_company' => !empty($input['company_name'])
        ]);
        
        // Commit transaction
        $database->commit();
        
        // Prepare response data
        $responseData = [
            'customer' => [
                'id' => $customerID,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'phone' => $phone,
                'address' => $address,
                'city' => $city,
                'state' => $state,
                'postal_code' => $postalCode,
                'country' => $country,
                'date_of_birth' => $dateOfBirth,
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        if ($companyID) {
            $responseData['company'] = [
                'id' => $companyID,
                'name' => $companyName,
                'registration_number' => $registrationNumber,
                'tax_id' => $taxID,
                'industry' => $industry,
                'website' => $website
            ];
        }
        
        ApiResponse::success($responseData, 'Customer created successfully', 201);
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Create customer error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            SessionManager::getUserId(),
            'CREATE_CUSTOMER_ERROR',
            $e->getMessage(),
            $e->getTraceAsString(),
            $input ?? []
        );
    }
    
    ApiResponse::serverError('An error occurred while creating the customer');
}
?>
