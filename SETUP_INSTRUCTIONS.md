# ZamSend Courier Services - Setup Instructions

## Overview
ZamSend is a comprehensive international courier services management system built for the LIS3010 assignment. The system provides a complete solution for managing shipments, customers, users, and tracking packages with real-time updates.

## System Features

### ✅ **Completed Features:**

#### 1. **Professional Website Design**
- Modern, responsive courier services website
- ZamSend branding and courier-focused content
- Professional navigation and user interface
- Mobile-friendly design
- Complete page transformation from educational to courier theme

#### 2. **Authentication System**
- Secure login with password hashing
- Two-Factor Authentication (2FA) simulation
- Role-based access control (Super Admin, Sub-Admin, Customer Service, Logistics)
- Session management with timeout
- Secure logout functionality

#### 3. **Package Tracking System**
- Real-time package tracking interface
- Comprehensive tracking API
- Progress indicators and timeline view
- Public tracking (no login required)
- Detailed shipment information display

#### 4. **Database Architecture**
- 14 comprehensive database tables
- Proper relationships and constraints
- Sample data with 20+ shipments
- User permissions system
- Analytics and error logging

#### 5. **Backend API System**
- RESTful API endpoints
- Comprehensive error handling
- Input validation and sanitization
- Analytics logging
- Database transaction management

#### 6. **Admin Dashboard**
- Super Admin dashboard with system overview
- Real-time statistics and charts
- User and customer management interfaces
- System monitoring and alerts
- Comprehensive reporting capabilities

#### 7. **Customer Management**
- Customer creation and management
- Company profile support
- Search and pagination
- Role-based permissions

#### 8. **Shipment Management**
- Shipment creation with validation
- Automatic tracking number generation
- Cost calculation
- Status tracking and updates

#### 9. **Complete Page System**
- Homepage with courier services content
- Services page with detailed offerings
- About page with company information
- Contact page with business details
- Pricing page with shipping rates
- Blog page with shipping news
- Ship Now page for creating shipments
- Calculator page for cost estimation
- User dashboard for customers
- Admin dashboard for system management

## File Structure

```
ZamShipment/
├── index.html                 # Homepage with courier services content
├── login.html                 # User authentication page
├── dashboard.html             # User dashboard
├── tracking.html              # Package tracking interface
├── services.html              # Services showcase page
├── about.html                 # Company information page
├── contact.html               # Contact information page
├── pricing.html               # Shipping rates and pricing
├── blog.html                  # Shipping news and updates
├── ship-now.html              # Shipment creation form
├── calculator.html            # Shipping cost calculator
├── admin/
│   └── dashboard.html         # Admin dashboard
├── api/
│   ├── config/
│   │   └── database.php       # Database configuration and utilities
│   ├── auth/
│   │   ├── login.php          # User authentication
│   │   ├── logout.php         # User logout
│   │   └── check-session.php  # Session validation
│   ├── tracking/
│   │   └── track.php          # Package tracking API
│   ├── customers/
│   │   ├── create.php         # Customer creation
│   │   └── list.php           # Customer listing
│   ├── shipments/
│   │   ├── create.php         # Shipment creation
│   │   └── list.php           # Shipment listing
│   └── admin/
│       └── dashboard-stats.php # Admin statistics
├── database_setup.sql         # Database schema
├── sample_data.sql           # Sample data insertion
├── SETUP_INSTRUCTIONS.md     # Complete setup guide
└── css/, js/, images/        # Assets and resources
```

## Database Setup Instructions

### Prerequisites
1. **XAMPP** installed and running
2. **MySQL** service started
3. **Apache** service started

### Step 1: Create Database
1. Open phpMyAdmin (http://localhost/phpmyadmin)
2. Create a new database named `courier_db`
3. Or use MySQL command line:
   ```sql
   CREATE DATABASE courier_db;
   ```

### Step 2: Import Database Schema
1. **Option A - phpMyAdmin:**
   - Select the `courier_db` database
   - Click "Import" tab
   - Choose `database_setup.sql` file
   - Click "Go" to execute

2. **Option B - Command Line:**
   ```bash
   mysql -u root -p courier_db < database_setup.sql
   ```

### Step 3: Import Sample Data
1. **Option A - phpMyAdmin:**
   - Select the `courier_db` database
   - Click "Import" tab
   - Choose `sample_data.sql` file
   - Click "Go" to execute

2. **Option B - Command Line:**
   ```bash
   mysql -u root -p courier_db < sample_data.sql
   ```

## Default User Accounts

### Super Admin
- **Username:** `superadmin`
- **Email:** `<EMAIL>`
- **Password:** `SuperAdmin123!`
- **2FA:** Enabled (demo codes provided during login)

### Sub-Admin
- **Username:** `subadmin1`
- **Email:** `<EMAIL>`
- **Password:** `SubAdmin123!`

### Customer Service
- **Username:** `cs_staff1`
- **Email:** `<EMAIL>`
- **Password:** `SubAdmin123!`

## Testing the System

### 1. **Homepage**
- Visit: `http://localhost/ZamShipment/`
- Verify courier services content and navigation

### 2. **Package Tracking**
- Visit: `http://localhost/ZamShipment/tracking.html`
- Test with tracking number: `PKG-2024-001`
- Verify real-time tracking display

### 3. **User Login**
- Visit: `http://localhost/ZamShipment/login.html`
- Login with Super Admin credentials
- Test 2FA functionality (demo codes provided)

### 4. **Admin Dashboard**
- After login as Super Admin, visit: `http://localhost/ZamShipment/admin/dashboard.html`
- Verify statistics, charts, and system overview

### 5. **Services Page**
- Visit: `http://localhost/ZamShipment/services.html`
- Verify service offerings and pricing

### 6. **About Page**
- Visit: `http://localhost/ZamShipment/about.html`
- Verify company information and timeline

### 7. **Contact Page**
- Visit: `http://localhost/ZamShipment/contact.html`
- Verify contact information and form

### 8. **Pricing Page**
- Visit: `http://localhost/ZamShipment/pricing.html`
- Verify shipping rates and service plans

### 9. **Shipping Calculator**
- Visit: `http://localhost/ZamShipment/calculator.html`
- Test cost calculation with different parameters

### 10. **Ship Now Page**
- Visit: `http://localhost/ZamShipment/ship-now.html`
- Test shipment creation form (requires login)

### 11. **Blog/News Page**
- Visit: `http://localhost/ZamShipment/blog.html`
- Verify shipping news and updates

## API Endpoints

### Authentication
- `POST /api/auth/login.php` - User login
- `POST /api/auth/logout.php` - User logout
- `GET /api/auth/check-session.php` - Session validation

### Tracking
- `POST /api/tracking/track.php` - Track package

### Customers
- `POST /api/customers/create.php` - Create customer
- `GET /api/customers/list.php` - List customers

### Shipments
- `POST /api/shipments/create.php` - Create shipment
- `GET /api/shipments/list.php` - List shipments

### Admin
- `GET /api/admin/dashboard-stats.php` - Dashboard statistics

## Security Features

1. **Password Hashing:** All passwords use PHP's `password_hash()`
2. **Input Sanitization:** All user inputs are sanitized
3. **SQL Injection Prevention:** Prepared statements used
4. **Session Security:** Timeout and validation
5. **Role-Based Access:** Permissions system
6. **Error Logging:** Comprehensive error tracking
7. **Analytics Logging:** User activity tracking

## System Requirements

- **PHP:** 7.4 or higher
- **MySQL:** 5.7 or higher
- **Apache:** 2.4 or higher
- **Browser:** Modern browser with JavaScript enabled

## Troubleshooting

### Database Connection Issues
1. Verify XAMPP MySQL is running
2. Check database credentials in `api/config/database.php`
3. Ensure `courier_db` database exists

### Login Issues
1. Verify database is properly set up with sample data
2. Check browser console for JavaScript errors
3. Verify session configuration

### Tracking Issues
1. Ensure sample data is imported
2. Test with provided tracking numbers
3. Check API endpoints are accessible

## Future Enhancements

The system is designed to be extensible with additional features:
- Email notifications
- SMS alerts
- Payment integration
- Mobile app API
- Advanced reporting
- Multi-language support
- Real-time notifications

## Support

For technical support or questions about the system:
- Check the error logs in the database
- Review browser console for JavaScript errors
- Verify all files are properly uploaded
- Ensure proper file permissions

---

**ZamSend Courier Services**  
*Connecting Zambia to the World*  
Developed for LIS3010 Assignment - 2024
