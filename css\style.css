@font-face {
  font-family: 'icomoon';
  src: url("../fonts/icomoon/icomoon.eot?srf3rx");
  src: url("../fonts/icomoon/icomoon.eot?srf3rx#iefix") format("embedded-opentype"), url("../fonts/icomoon/icomoon.ttf?srf3rx") format("truetype"), url("../fonts/icomoon/icomoon.woff?srf3rx") format("woff"), url("../fonts/icomoon/icomoon.svg?srf3rx#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* =======================================================
*
* 	Template Style 
*
* ======================================================= */
body {
  font-family: "Work Sans", Arial, sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.7;
  color: #828282;
  background: #fff;
}

#page {
  position: relative;
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.offcanvas #page {
  overflow: hidden;
  position: absolute;
}
.offcanvas #page:after {
  -webkit-transition: 2s;
  -o-transition: 2s;
  transition: 2s;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 101;
  background: rgba(0, 0, 0, 0.7);
  content: "";
}

a {
  color: #EA9215;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
a:hover, a:active, a:focus {
  color: #EA9215;
  outline: none;
  text-decoration: none;
}

p {
  margin-bottom: 20px;
}

h1, h2, h3, h4, h5, h6, figure {
  color: #000;
  font-family: "Work Sans", Arial, sans-serif;
  font-weight: 400;
  margin: 0 0 20px 0;
}

::-webkit-selection {
  color: #fff;
  background: #EA9215;
}

::-moz-selection {
  color: #fff;
  background: #EA9215;
}

::selection {
  color: #fff;
  background: #EA9215;
}

.fh5co-nav {
  position: absolute;
  top: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  padding: 0;
  z-index: 1001;
}
.fh5co-nav .top-menu {
  padding: 28px 0;
}
.fh5co-nav .top {
  padding: 7px 0;
  margin-bottom: 0;
}
.fh5co-nav .top .num, .fh5co-nav .top .fh5co-social {
  display: inline-block;
  margin: 0;
}
.fh5co-nav .top .num {
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
  padding-right: 10px;
  margin-right: 5px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  letter-spacing: 0px;
}
.fh5co-nav .top .fh5co-social li {
  font-size: 14px;
}
.fh5co-nav .top .fh5co-social li a {
  padding: 0 7px;
}
.fh5co-nav .top .fh5co-social li a i {
  font-size: 14px;
}
.fh5co-nav #fh5co-logo {
  font-size: 24px;
  margin: 0;
  padding: 0;
  text-transform: uppercase;
  font-weight: bold;
  font-weight: 700;
  font-family: "Work Sans", Arial, sans-serif;
}
.fh5co-nav #fh5co-logo a span {
  color: #EA9215;
}
.fh5co-nav a {
  padding: 5px 10px;
  color: #fff;
}
@media screen and (max-width: 768px) {
  .fh5co-nav .menu-1 {
    display: none;
  }
}
.fh5co-nav ul {
  padding: 0;
  margin: 5px 0 0 0;
}
.fh5co-nav ul li {
  padding: 0;
  margin: 0;
  list-style: none;
  display: inline;
}
.fh5co-nav ul li a {
  font-size: 15px;
  padding: 30px 15px;
  color: rgba(255, 255, 255, 0.5);
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.fh5co-nav ul li a:hover, .fh5co-nav ul li a:focus, .fh5co-nav ul li a:active {
  color: white;
}
.fh5co-nav ul li.has-dropdown {
  position: relative;
}
.fh5co-nav ul li.has-dropdown .dropdown {
  width: 140px;
  -webkit-box-shadow: 0px 14px 33px -9px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 0px 14px 33px -9px rgba(0, 0, 0, 0.75);
  box-shadow: 0px 14px 33px -9px rgba(0, 0, 0, 0.75);
  z-index: 1002;
  visibility: hidden;
  opacity: 0;
  position: absolute;
  top: 40px;
  left: 0;
  text-align: left;
  background: #000;
  padding: 20px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: 0s;
  -o-transition: 0s;
  transition: 0s;
}
.fh5co-nav ul li.has-dropdown .dropdown:before {
  bottom: 100%;
  left: 40px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-bottom-color: #000;
  border-width: 8px;
  margin-left: -8px;
}
.fh5co-nav ul li.has-dropdown .dropdown li {
  display: block;
  margin-bottom: 7px;
}
.fh5co-nav ul li.has-dropdown .dropdown li:last-child {
  margin-bottom: 0;
}
.fh5co-nav ul li.has-dropdown .dropdown li a {
  padding: 2px 0;
  display: block;
  color: #999999;
  line-height: 1.2;
  text-transform: none;
  font-size: 13px;
  letter-spacing: 0;
}
.fh5co-nav ul li.has-dropdown .dropdown li a:hover {
  color: #fff;
}
.fh5co-nav ul li.has-dropdown:hover a, .fh5co-nav ul li.has-dropdown:focus a {
  color: #fff;
}
.fh5co-nav ul li.btn-cta a {
  padding: 30px 0px !important;
  color: #fff;
}
.fh5co-nav ul li.btn-cta a span {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 10px;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  -ms-border-radius: 100px;
  border-radius: 100px;
}
.fh5co-nav ul li.btn-cta a:hover span {
  -webkit-box-shadow: 0px 14px 20px -9px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 0px 14px 20px -9px rgba(0, 0, 0, 0.75);
  box-shadow: 0px 14px 20px -9px rgba(0, 0, 0, 0.75);
}
.fh5co-nav ul li.active > a {
  color: #fff !important;
}

#fh5co-header,
#fh5co-counter,
.fh5co-bg {
  background-size: cover;
  background-position: top center;
  background-repeat: no-repeat;
  position: relative;
}

.fh5co-bg {
  background-size: cover;
  background-position: center center;
  position: relative;
  width: 100%;
  float: left;
}

.fh5co-video {
  overflow: hidden;
}
@media screen and (max-width: 992px) {
  .fh5co-video {
    height: 450px;
  }
}
.fh5co-video a {
  z-index: 1001;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -45px;
  margin-left: -45px;
  width: 90px;
  height: 90px;
  display: table;
  text-align: center;
  background: #fff;
  -webkit-box-shadow: 0px 14px 30px -15px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 0px 14px 30px -15px rgba(0, 0, 0, 0.75);
  box-shadow: 0px 14px 30px -15px rgba(0, 0, 0, 0.75);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
.fh5co-video a i {
  text-align: center;
  display: table-cell;
  vertical-align: middle;
  font-size: 40px;
}
.fh5co-video .overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.fh5co-video:hover .overlay {
  background: rgba(0, 0, 0, 0.7);
}
.fh5co-video:hover a {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.fh5co-cover {
  height: 800px;
  background-size: cover;
  background-position: top center;
  background-repeat: no-repeat;
  position: relative;
  float: left;
  width: 100%;
}
.fh5co-cover .overlay {
  z-index: 0;
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.4);
}
.fh5co-cover > .fh5co-container {
  position: relative;
  z-index: 10;
}
@media screen and (max-width: 768px) {
  .fh5co-cover {
    height: 600px;
  }
}
.fh5co-cover .display-t,
.fh5co-cover .display-tc {
  z-index: 9;
  height: 700px;
  display: table;
  width: 100%;
  margin-top: 100px;
}
@media screen and (max-width: 768px) {
  .fh5co-cover .display-t,
  .fh5co-cover .display-tc {
    height: 600px;
  }
}
.fh5co-cover.fh5co-cover-sm {
  height: 600px;
}
@media screen and (max-width: 768px) {
  .fh5co-cover.fh5co-cover-sm {
    height: 400px;
  }
}
.fh5co-cover.fh5co-cover-sm .display-t,
.fh5co-cover.fh5co-cover-sm .display-tc {
  height: 600px;
  display: table;
  width: 100%;
}
@media screen and (max-width: 768px) {
  .fh5co-cover.fh5co-cover-sm .display-t,
  .fh5co-cover.fh5co-cover-sm .display-tc {
    height: 400px;
  }
}

#fh5co-header .display-tc,
#fh5co-counter .display-tc,
.fh5co-cover .display-tc {
  display: table-cell !important;
  vertical-align: middle;
}
#fh5co-header .display-tc h1, #fh5co-header .display-tc h2,
#fh5co-counter .display-tc h1,
#fh5co-counter .display-tc h2,
.fh5co-cover .display-tc h1,
.fh5co-cover .display-tc h2 {
  margin: 0;
  padding: 0;
  color: white;
}
#fh5co-header .display-tc h1,
#fh5co-counter .display-tc h1,
.fh5co-cover .display-tc h1 {
  margin-bottom: 20px;
  font-size: 54px;
  line-height: 1.3;
  font-weight: 300;
}
@media screen and (max-width: 768px) {
  #fh5co-header .display-tc h1,
  #fh5co-counter .display-tc h1,
  .fh5co-cover .display-tc h1 {
    font-size: 30px;
  }
}
#fh5co-header .display-tc h2,
#fh5co-counter .display-tc h2,
.fh5co-cover .display-tc h2 {
  font-size: 20px;
  line-height: 1.5;
  margin-bottom: 30px;
}
#fh5co-header .display-tc .btn,
#fh5co-counter .display-tc .btn,
.fh5co-cover .display-tc .btn {
  padding: 15px 30px;
  color: #fff;
  border: none !important;
  font-size: 18px;
}
#fh5co-header .display-tc .btn.btn-video,
#fh5co-counter .display-tc .btn.btn-video,
.fh5co-cover .display-tc .btn.btn-video {
  background: rgba(9, 198, 171, 0.8);
  border: none;
}
#fh5co-header .display-tc .btn.btn-video:hover, #fh5co-header .display-tc .btn.btn-video:focus,
#fh5co-counter .display-tc .btn.btn-video:hover,
#fh5co-counter .display-tc .btn.btn-video:focus,
.fh5co-cover .display-tc .btn.btn-video:hover,
.fh5co-cover .display-tc .btn.btn-video:focus {
  background: rgba(9, 198, 171, 0.9) !important;
  color: #fff !important;
}
#fh5co-header .display-tc .btn:hover, #fh5co-header .display-tc .btn:focus,
#fh5co-counter .display-tc .btn:hover,
#fh5co-counter .display-tc .btn:focus,
.fh5co-cover .display-tc .btn:hover,
.fh5co-cover .display-tc .btn:focus {
  color: #fff !important;
}
#fh5co-header .display-tc .btn.btn-learn,
#fh5co-counter .display-tc .btn.btn-learn,
.fh5co-cover .display-tc .btn.btn-learn {
  background: rgba(234, 146, 21, 0.8);
  border: none;
}
#fh5co-header .display-tc .btn.btn-learn:hover, #fh5co-header .display-tc .btn.btn-learn:focus,
#fh5co-counter .display-tc .btn.btn-learn:hover,
#fh5co-counter .display-tc .btn.btn-learn:focus,
.fh5co-cover .display-tc .btn.btn-learn:hover,
.fh5co-cover .display-tc .btn.btn-learn:focus {
  background: rgba(234, 146, 21, 0.9) !important;
  color: #fff !important;
}
#fh5co-header .display-tc .btn:hover,
#fh5co-counter .display-tc .btn:hover,
.fh5co-cover .display-tc .btn:hover {
  background: #EA9215 !important;
  -webkit-box-shadow: 0px 14px 30px -15px rgba(0, 0, 0, 0.75) !important;
  -moz-box-shadow: 0px 14px 30px -15px rgba(0, 0, 0, 0.75) !important;
  box-shadow: 0px 14px 30px -15px rgba(0, 0, 0, 0.75) !important;
}
#fh5co-header .display-tc .form-inline .form-group,
#fh5co-counter .display-tc .form-inline .form-group,
.fh5co-cover .display-tc .form-inline .form-group {
  width: 100% !important;
  margin-bottom: 10px;
}
#fh5co-header .display-tc .form-inline .form-group .form-control,
#fh5co-counter .display-tc .form-inline .form-group .form-control,
.fh5co-cover .display-tc .form-inline .form-group .form-control {
  width: 100%;
  background: #fff;
  border: none;
}

.fh5co-bg-section {
  background: rgba(0, 0, 0, 0.05);
}

#fh5co-explore,
#fh5co-pricing,
#fh5co-testimonial,
#fh5co-started,
#fh5co-project,
#fh5co-blog,
#fh5co-contact,
#fh5co-footer {
  padding: 7em 0;
  clear: both;
}
@media screen and (max-width: 768px) {
  #fh5co-explore,
  #fh5co-pricing,
  #fh5co-testimonial,
  #fh5co-started,
  #fh5co-project,
  #fh5co-blog,
  #fh5co-contact,
  #fh5co-footer {
    padding: 3em 0;
  }
}

#fh5co-counter {
  padding: 3em 0;
  clear: both;
}

#fh5co-steps {
  clear: both;
  padding-bottom: 7em;
}

.fh5co-counters {
  padding: 3em 0;
  background-size: cover;
  background-attachment: fixed;
  background-position: center center;
}
.fh5co-counters .counter-wrap {
  border: 1px solid red !important;
}
.fh5co-counters .fh5co-counter {
  font-size: 44px;
  display: block;
  color: rgba(0, 0, 0, 0.7);
  font-family: "Work Sans", Arial, sans-serif;
  width: 100%;
  font-weight: 400;
  margin-bottom: .3em;
}
.fh5co-counters .fh5co-counter-label {
  color: rgba(0, 0, 0, 0.5);
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 5px;
  margin-bottom: 2em;
  display: block;
}

#fh5co-explore .fh5co-explore1 {
  margin-bottom: 7em;
}

.mt {
  margin-top: 70px;
}
.mt h4 {
  position: relative;
  padding-left: 40px;
  font-size: 20px;
}
.mt h4 i {
  position: absolute;
  left: 0;
  top: 0;
  color: #EA9215;
}
.mt > div {
  margin-bottom: 40px;
}
.mt .list-nav {
  margin: 50px 0 30px 0;
  padding: 0;
}
.mt .list-nav li {
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 16px;
  padding-left: 30px;
  margin-bottom: 10px;
  position: relative;
}
.mt .list-nav li i {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 18px;
  color: #EA9215;
}

.fh5co-social-icons {
  margin: 0;
  padding: 0;
}
.fh5co-social-icons li {
  margin: 0;
  padding: 0;
  list-style: none;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
}
.fh5co-social-icons li a {
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
  color: #EA9215;
  padding-left: 10px;
  padding-right: 10px;
}
.fh5co-social-icons li a i {
  font-size: 20px;
}

.fh5co-contact-info ul {
  padding: 0;
  margin: 0;
}
.fh5co-contact-info ul li {
  padding: 0 0 0 40px;
  margin: 0 0 30px 0;
  list-style: none;
  position: relative;
}
.fh5co-contact-info ul li:before {
  color: #EA9215;
  position: absolute;
  left: 0;
  top: .05em;
  font-family: 'icomoon';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.fh5co-contact-info ul li.address:before {
  font-size: 30px;
  content: "\e9d1";
}
.fh5co-contact-info ul li.phone:before {
  font-size: 23px;
  content: "\e9f4";
}
.fh5co-contact-info ul li.email:before {
  font-size: 23px;
  content: "\e9da";
}
.fh5co-contact-info ul li.url:before {
  font-size: 23px;
  content: "\e9af";
}

.proj-bottom {
  padding-bottom: 4em;
}

.fh5co-project {
  margin-bottom: 30px;
}
.fh5co-project > a {
  display: block;
  color: #000;
  position: relative;
  bottom: 0;
  overflow: hidden;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.fh5co-project > a img {
  position: relative;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.fh5co-project > a:after {
  opacity: 0;
  visibility: hidden;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  -webkit-box-shadow: inset 0px -34px 98px 8px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: inset 0px -34px 98px 8px rgba(0, 0, 0, 0.75);
  -ms-box-shadow: inset 0px -34px 98px 8px rgba(0, 0, 0, 0.75);
  -o-box-shadow: inset 0px -34px 98px 8px rgba(0, 0, 0, 0.75);
  box-shadow: inset 0px -34px 98px 8px rgba(0, 0, 0, 0.75);
  z-index: 8;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.fh5co-project > a h3, .fh5co-project > a span {
  z-index: 12;
  position: absolute;
  right: 20px;
  left: 20px;
  bottom: 50px;
  color: #fff;
  margin: 0;
  padding: 0;
  opacity: 1;
  font-size: 30px;
  font-weight: 300;
  visibility: visible;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}
.fh5co-project > a span {
  font-size: 14px;
  bottom: 20px;
}
.fh5co-project > a:hover {
  -webkit-box-shadow: 0px 18px 71px -10px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 0px 18px 71px -10px rgba(0, 0, 0, 0.75);
  box-shadow: 0px 18px 71px -10px rgba(0, 0, 0, 0.75);
}
.fh5co-project > a:hover:after {
  opacity: 1;
  visibility: visible;
}
@media screen and (max-width: 768px) {
  .fh5co-project > a:hover:after {
    opacity: 0;
    visibility: hidden;
  }
}
.fh5co-project > a:hover h3, .fh5co-project > a:hover span {
  opacity: 1;
  bottom: 55px;
}
.fh5co-project > a:hover span {
  bottom: 15px;
}
.fh5co-project > a:hover img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
@media screen and (max-width: 768px) {
  .fh5co-project > a:hover img {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
}

.features {
  margin-bottom: 3em;
  width: 100%;
  float: left;
}

.fh5co-heading {
  margin-bottom: 5em;
}
.fh5co-heading.fh5co-heading-sm {
  margin-bottom: 2em;
}
.fh5co-heading h2 {
  font-size: 32px;
  margin-bottom: 20px;
  line-height: 1.5;
  color: #000;
}
.fh5co-heading p {
  font-size: 18px;
  line-height: 1.5;
  color: #828282;
}
.fh5co-heading span {
  display: block;
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 2px;
}

#fh5co-testimonial {
  background: #efefef;
}
#fh5co-testimonial .testimony-slide {
  text-align: center;
}
#fh5co-testimonial .testimony-slide span {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 700;
  display: block;
}
#fh5co-testimonial .testimony-slide figure {
  margin-bottom: 10px;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
}
#fh5co-testimonial .testimony-slide figure img {
  width: 100px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
#fh5co-testimonial .testimony-slide blockquote {
  border: none;
  margin: 30px auto;
  width: 70%;
  position: relative;
  padding: 0;
}
@media screen and (max-width: 992px) {
  #fh5co-testimonial .testimony-slide blockquote {
    width: 100%;
  }
}
#fh5co-testimonial .arrow-thumb {
  position: absolute;
  top: 40%;
  display: block;
  width: 100%;
}
#fh5co-testimonial .arrow-thumb a {
  font-size: 32px;
  color: #dadada;
}
#fh5co-testimonial .arrow-thumb a:hover, #fh5co-testimonial .arrow-thumb a:focus, #fh5co-testimonial .arrow-thumb a:active {
  text-decoration: none;
}

.bs-wizard {
  margin-top: 40px;
}

/*Form Wizard*/
.bs-wizard {
  border-bottom: solid 1px #e0e0e0;
  padding: 0 0 10px 0;
}

.bs-wizard > .bs-wizard-step {
  padding: 0;
  position: relative;
}

.bs-wizard > .bs-wizard-step .bs-wizard-stepnum {
  font-size: 16px;
  margin-bottom: 10px;
}

.bs-wizard > .bs-wizard-step .bs-wizard-info {
  color: #999;
  font-size: 14px;
  padding: 20px;
}

.bs-wizard > .bs-wizard-step > .bs-wizard-dot {
  position: absolute;
  width: 30px;
  height: 30px;
  display: block;
  background: #fbe8aa;
  top: 45px;
  left: 50%;
  margin-top: -15px;
  margin-left: -15px;
  border-radius: 50%;
}

.bs-wizard > .bs-wizard-step > .bs-wizard-dot:after {
  content: ' ';
  width: 14px;
  height: 14px;
  background: #fbbd19;
  border-radius: 50px;
  position: absolute;
  top: 8px;
  left: 8px;
}

.bs-wizard > .bs-wizard-step > .progress {
  position: relative;
  border-radius: 0px;
  height: 8px;
  box-shadow: none;
  margin: 22px 0;
}

.bs-wizard > .bs-wizard-step > .progress > .progress-bar {
  width: 0px;
  box-shadow: none;
  background: #fbe8aa;
}

.bs-wizard > .bs-wizard-step.complete > .progress > .progress-bar {
  width: 100%;
}

.bs-wizard > .bs-wizard-step.active > .progress > .progress-bar {
  width: 50%;
}

.bs-wizard > .bs-wizard-step:first-child.active > .progress > .progress-bar {
  width: 0%;
}

.bs-wizard > .bs-wizard-step:last-child.active > .progress > .progress-bar {
  width: 100%;
}

.bs-wizard > .bs-wizard-step.disabled > .bs-wizard-dot {
  background-color: #f5f5f5;
}

.bs-wizard > .bs-wizard-step.disabled > .bs-wizard-dot:after {
  opacity: 0;
}

.bs-wizard > .bs-wizard-step:first-child > .progress {
  left: 50%;
  width: 50%;
}

.bs-wizard > .bs-wizard-step:last-child > .progress {
  width: 50%;
}

.bs-wizard > .bs-wizard-step.disabled a.bs-wizard-dot {
  pointer-events: none;
}

#fh5co-started {
  background-size: cover;
  background-position: top center;
  background-repeat: no-repeat;
  position: relative;
  float: left;
  width: 100%;
}
#fh5co-started .overlay {
  z-index: 0;
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
}
#fh5co-started .fh5co-heading h2 {
  color: #fff;
  margin-bottom: 20px !important;
}
#fh5co-started .fh5co-heading p {
  color: rgba(255, 255, 255, 0.5);
}
#fh5co-started .form-control {
  background: rgba(255, 255, 255, 0.2);
  border: none !important;
  color: #fff;
  font-size: 16px !important;
  width: 100%;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
#fh5co-started .form-control::-webkit-input-placeholder {
  color: #fff;
}
#fh5co-started .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: #fff;
}
#fh5co-started .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #fff;
}
#fh5co-started .form-control:-ms-input-placeholder {
  color: #fff;
}
#fh5co-started .form-control:focus {
  background: rgba(255, 255, 255, 0.3);
}
#fh5co-started .btn {
  height: 54px;
  border: none !important;
  background: #EA9215;
  color: #fff;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 400;
  padding-left: 50px;
  padding-right: 50px;
}
#fh5co-started .form-inline .form-group {
  width: 100% !important;
  margin-bottom: 10px;
}
#fh5co-started .form-inline .form-group .form-control {
  width: 100%;
}

.fh5co-blog {
  margin-bottom: 60px;
}
.fh5co-blog > a {
  display: block;
  position: relative;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.fh5co-blog > a img {
  width: 100%;
}
@media screen and (max-width: 768px) {
  .fh5co-blog {
    width: 100%;
  }
}
.fh5co-blog .blog-text {
  margin-bottom: 30px;
  position: relative;
  background: #fff;
  width: 100%;
  padding: 30px;
  float: left;
  -webkit-box-shadow: 0px 10px 20px -12px rgba(0, 0, 0, 0.18);
  -moz-box-shadow: 0px 10px 20px -12px rgba(0, 0, 0, 0.18);
  box-shadow: 0px 10px 20px -12px rgba(0, 0, 0, 0.18);
}
.fh5co-blog .blog-text span {
  display: inline-block;
  margin-bottom: 20px;
}
.fh5co-blog .blog-text span.comment {
  float: right;
}
.fh5co-blog .blog-text span.comment a {
  color: rgba(0, 0, 0, 0.3);
}
.fh5co-blog .blog-text span.comment a i {
  color: #EA9215;
  padding-left: 7px;
}
.fh5co-blog .blog-text h3 {
  font-size: 20px;
  margin-bottom: 20px;
  line-height: 1.5;
}
.fh5co-blog .blog-text h3 a {
  color: black;
}

.pricing {
  display: block;
  float: left;
  margin-bottom: 30px;
  width: 100%;
}

.price-box {
  width: 100%;
  text-align: center;
  padding: 30px;
  background: rgba(0, 0, 0, 0.05);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  margin-bottom: 40px;
  position: relative;
}
.price-box.popular .btn-select-plan {
  background: #EA9215;
}
.price-box.popular .price {
  color: #EA9215;
}
.price-box .btn-select-plan {
  padding: 10px 20px;
  background: #09C6AB;
  color: #fff;
}
.price-box .classes {
  padding: 0;
}
.price-box .classes li {
  display: block;
  width: 100%;
  list-style: none;
  margin: 0;
  font-size: 16px;
  padding: 8px 10px;
}
.price-box .classes li.color {
  background: rgba(0, 0, 0, 0.04);
}

.pricing-plan {
  margin: 0 0 50px 0;
  padding: 0;
  font-size: 13px;
  letter-spacing: 2px;
  text-transform: uppercase;
  font-weight: 700;
  color: #888f94;
}
.pricing-plan.pricing-plan-offer {
  margin-bottom: 24px;
}
.pricing-plan span {
  display: block;
  margin-top: 10px;
  margin-bottom: 0;
  color: #d5d8db;
}

.price {
  font-size: 72px;
  color: #000;
  line-height: 50px;
}
.price .currency {
  font-size: 30px;
  top: -0.9em;
  padding-right: 10px;
}
.price small {
  font-size: 13px;
  display: block;
  text-transform: uppercase;
  color: #888f94;
}

#fh5co-footer {
  background: #efefef;
}
#fh5co-footer .fh5co-footer-links {
  padding: 0;
  margin: 0;
}
#fh5co-footer .fh5co-footer-links li {
  padding: 0;
  margin: 0;
  list-style: none;
}
#fh5co-footer .fh5co-footer-links li a {
  color: rgba(0, 0, 0, 0.5);
  text-decoration: none;
}
#fh5co-footer .fh5co-footer-links li a:hover {
  text-decoration: underline;
}
#fh5co-footer .fh5co-widget {
  margin-bottom: 30px;
}
@media screen and (max-width: 768px) {
  #fh5co-footer .fh5co-widget {
    text-align: left;
  }
}
#fh5co-footer .fh5co-widget h3 {
  margin-bottom: 15px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 2px;
  text-transform: uppercase;
}
#fh5co-footer .copyright .block {
  display: block;
}

#map {
  width: 100%;
  height: 500px;
  position: relative;
}
@media screen and (max-width: 768px) {
  #map {
    height: 200px;
  }
}

#fh5co-offcanvas {
  position: absolute;
  z-index: 1901;
  width: 270px;
  background: black;
  top: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 75px 40px 40px 40px;
  overflow-y: auto;
  display: none;
  -moz-transform: translateX(270px);
  -webkit-transform: translateX(270px);
  -ms-transform: translateX(270px);
  -o-transform: translateX(270px);
  transform: translateX(270px);
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
@media screen and (max-width: 768px) {
  #fh5co-offcanvas {
    display: block;
  }
}
.offcanvas #fh5co-offcanvas {
  -moz-transform: translateX(0px);
  -webkit-transform: translateX(0px);
  -ms-transform: translateX(0px);
  -o-transform: translateX(0px);
  transform: translateX(0px);
}
#fh5co-offcanvas a {
  color: rgba(255, 255, 255, 0.5);
}
#fh5co-offcanvas a:hover {
  color: rgba(255, 255, 255, 0.8);
}
#fh5co-offcanvas ul {
  padding: 0;
  margin: 0;
}
#fh5co-offcanvas ul li {
  padding: 0;
  margin: 0;
  list-style: none;
}
#fh5co-offcanvas ul li > ul {
  padding-left: 20px;
  display: none;
}
#fh5co-offcanvas ul li.offcanvas-has-dropdown > a {
  display: block;
  position: relative;
}
#fh5co-offcanvas ul li.offcanvas-has-dropdown > a:after {
  position: absolute;
  right: 0px;
  font-family: 'icomoon';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e921";
  font-size: 20px;
  color: rgba(255, 255, 255, 0.2);
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
#fh5co-offcanvas ul li.offcanvas-has-dropdown.active a:after {
  -webkit-transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  -o-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.uppercase {
  font-size: 14px;
  color: #000;
  margin-bottom: 10px;
  font-weight: 700;
  text-transform: uppercase;
}

.gototop {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}
.gototop.active {
  opacity: 1;
  visibility: visible;
}
.gototop a {
  width: 50px;
  height: 50px;
  display: table;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  text-align: center;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}
.gototop a i {
  height: 50px;
  display: table-cell;
  vertical-align: middle;
}
.gototop a:hover, .gototop a:active, .gototop a:focus {
  text-decoration: none;
  outline: none;
}

.fh5co-nav-toggle {
  width: 25px;
  height: 25px;
  cursor: pointer;
  text-decoration: none;
}
.fh5co-nav-toggle.active i::before, .fh5co-nav-toggle.active i::after {
  background: #444;
}
.fh5co-nav-toggle:hover, .fh5co-nav-toggle:focus, .fh5co-nav-toggle:active {
  outline: none;
  border-bottom: none !important;
}
.fh5co-nav-toggle i {
  position: relative;
  display: inline-block;
  width: 25px;
  height: 2px;
  color: #252525;
  font: bold 14px/.4 Helvetica;
  text-transform: uppercase;
  text-indent: -55px;
  background: #252525;
  transition: all .2s ease-out;
}
.fh5co-nav-toggle i::before, .fh5co-nav-toggle i::after {
  content: '';
  width: 25px;
  height: 2px;
  background: #252525;
  position: absolute;
  left: 0;
  transition: all .2s ease-out;
}
.fh5co-nav-toggle.fh5co-nav-white > i {
  color: #fff;
  background: #fff;
}
.fh5co-nav-toggle.fh5co-nav-white > i::before, .fh5co-nav-toggle.fh5co-nav-white > i::after {
  background: #fff;
}

.fh5co-nav-toggle i::before {
  top: -7px;
}

.fh5co-nav-toggle i::after {
  bottom: -7px;
}

.fh5co-nav-toggle:hover i::before {
  top: -10px;
}

.fh5co-nav-toggle:hover i::after {
  bottom: -10px;
}

.fh5co-nav-toggle.active i {
  background: transparent;
}

.fh5co-nav-toggle.active i::before {
  top: 0;
  -webkit-transform: rotateZ(45deg);
  -moz-transform: rotateZ(45deg);
  -ms-transform: rotateZ(45deg);
  -o-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}

.fh5co-nav-toggle.active i::after {
  bottom: 0;
  -webkit-transform: rotateZ(-45deg);
  -moz-transform: rotateZ(-45deg);
  -ms-transform: rotateZ(-45deg);
  -o-transform: rotateZ(-45deg);
  transform: rotateZ(-45deg);
}

.fh5co-nav-toggle {
  position: absolute;
  right: 0px;
  top: 65px;
  z-index: 21;
  padding: 6px 0 0 0;
  display: block;
  margin: 0 auto;
  display: none;
  height: 44px;
  width: 44px;
  z-index: 2001;
  border-bottom: none !important;
}
@media screen and (max-width: 768px) {
  .fh5co-nav-toggle {
    display: block;
  }
}

.btn {
  margin-right: 4px;
  margin-bottom: 4px;
  font-family: "Work Sans", Arial, sans-serif;
  font-size: 16px;
  font-weight: 400;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  padding: 8px 20px;
}
.btn.btn-md {
  padding: 8px 20px !important;
}
.btn.btn-lg {
  padding: 18px 36px !important;
}
.btn:hover, .btn:active, .btn:focus {
  box-shadow: none !important;
  outline: none !important;
}

.btn-primary {
  background: #EA9215;
  color: #fff;
  border: 2px solid #EA9215;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
  background: #ec9d2c !important;
  border-color: #ec9d2c !important;
}
.btn-primary.btn-outline {
  background: transparent;
  color: #EA9215;
  border: 2px solid #EA9215;
}
.btn-primary.btn-outline:hover, .btn-primary.btn-outline:focus, .btn-primary.btn-outline:active {
  background: #EA9215;
  color: #fff;
}

.btn-success {
  background: #5cb85c;
  color: #fff;
  border: 2px solid #5cb85c;
}
.btn-success:hover, .btn-success:focus, .btn-success:active {
  background: #4cae4c !important;
  border-color: #4cae4c !important;
}
.btn-success.btn-outline {
  background: transparent;
  color: #5cb85c;
  border: 2px solid #5cb85c;
}
.btn-success.btn-outline:hover, .btn-success.btn-outline:focus, .btn-success.btn-outline:active {
  background: #5cb85c;
  color: #fff;
}

.btn-info {
  background: #5bc0de;
  color: #fff;
  border: 2px solid #5bc0de;
}
.btn-info:hover, .btn-info:focus, .btn-info:active {
  background: #46b8da !important;
  border-color: #46b8da !important;
}
.btn-info.btn-outline {
  background: transparent;
  color: #5bc0de;
  border: 2px solid #5bc0de;
}
.btn-info.btn-outline:hover, .btn-info.btn-outline:focus, .btn-info.btn-outline:active {
  background: #5bc0de;
  color: #fff;
}

.btn-warning {
  background: #f0ad4e;
  color: #fff;
  border: 2px solid #f0ad4e;
}
.btn-warning:hover, .btn-warning:focus, .btn-warning:active {
  background: #eea236 !important;
  border-color: #eea236 !important;
}
.btn-warning.btn-outline {
  background: transparent;
  color: #f0ad4e;
  border: 2px solid #f0ad4e;
}
.btn-warning.btn-outline:hover, .btn-warning.btn-outline:focus, .btn-warning.btn-outline:active {
  background: #f0ad4e;
  color: #fff;
}

.btn-danger {
  background: #d9534f;
  color: #fff;
  border: 2px solid #d9534f;
}
.btn-danger:hover, .btn-danger:focus, .btn-danger:active {
  background: #d43f3a !important;
  border-color: #d43f3a !important;
}
.btn-danger.btn-outline {
  background: transparent;
  color: #d9534f;
  border: 2px solid #d9534f;
}
.btn-danger.btn-outline:hover, .btn-danger.btn-outline:focus, .btn-danger.btn-outline:active {
  background: #d9534f;
  color: #fff;
}

.btn-outline {
  background: none;
  border: 2px solid gray;
  font-size: 16px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}
.btn-outline:hover, .btn-outline:focus, .btn-outline:active {
  box-shadow: none;
}

.btn.with-arrow {
  position: relative;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}
.btn.with-arrow i {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  right: 0px;
  top: 50%;
  margin-top: -8px;
  -webkit-transition: 0.2s;
  -o-transition: 0.2s;
  transition: 0.2s;
}
.btn.with-arrow:hover {
  padding-right: 50px;
}
.btn.with-arrow:hover i {
  color: #fff;
  right: 18px;
  visibility: visible;
  opacity: 1;
}

.form-control {
  box-shadow: none;
  background: transparent;
  border: 2px solid rgba(0, 0, 0, 0.1);
  height: 54px;
  font-size: 18px;
  font-weight: 300;
}
.form-control:active, .form-control:focus {
  outline: none;
  box-shadow: none;
  border-color: #EA9215;
}

.row-pb-md {
  padding-bottom: 4em !important;
}

.row-pb-sm {
  padding-bottom: 2em !important;
}

.fh5co-loader {
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: url(../images/loader.gif) center no-repeat #fff;
}

.js .animate-box {
  opacity: 0;
}

/*# sourceMappingURL=style.css.map */
