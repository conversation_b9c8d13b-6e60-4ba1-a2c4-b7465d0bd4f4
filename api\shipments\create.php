<?php
/**
 * Create Shipment API
 * Handles creation of new shipments
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Require authentication
    SessionManager::requireLogin();
    $userID = SessionManager::getUserId();
    $userRole = SessionManager::getUserRole();
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST; // Fallback to form data
    }
    
    // Validate required fields
    $requiredFields = [
        'customer_id', 'sender_name', 'sender_address', 'sender_phone',
        'recipient_name', 'recipient_address', 'recipient_phone',
        'shipment_type', 'delivery_mode', 'weight'
    ];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            ApiResponse::error("Field '$field' is required");
        }
    }
    
    // Sanitize input data
    $customerID = (int)$input['customer_id'];
    $senderName = Database::sanitize($input['sender_name']);
    $senderAddress = Database::sanitize($input['sender_address']);
    $senderPhone = Database::sanitize($input['sender_phone']);
    $recipientName = Database::sanitize($input['recipient_name']);
    $recipientAddress = Database::sanitize($input['recipient_address']);
    $recipientPhone = Database::sanitize($input['recipient_phone']);
    $shipmentType = Database::sanitize($input['shipment_type']);
    $deliveryMode = Database::sanitize($input['delivery_mode']);
    $weight = (float)$input['weight'];
    
    // Optional fields
    $dimensions = Database::sanitize($input['dimensions'] ?? '');
    $declaredValue = isset($input['declared_value']) ? (float)$input['declared_value'] : null;
    $priority = Database::sanitize($input['priority'] ?? 'Medium');
    $specialInstructions = Database::sanitize($input['special_instructions'] ?? '');
    
    // Validate data
    if (!Database::validatePhone($senderPhone)) {
        ApiResponse::error('Invalid sender phone number format');
    }
    
    if (!Database::validatePhone($recipientPhone)) {
        ApiResponse::error('Invalid recipient phone number format');
    }
    
    if ($weight <= 0) {
        ApiResponse::error('Weight must be a positive number');
    }
    
    if ($weight > 50) {
        ApiResponse::error('Weight cannot exceed 50kg for regular shipments');
    }
    
    // Validate enum values
    $validShipmentTypes = ['Express', 'Standard', 'Special Handling'];
    if (!in_array($shipmentType, $validShipmentTypes)) {
        ApiResponse::error('Invalid shipment type');
    }
    
    $validDeliveryModes = ['Air', 'Road', 'Rail', 'Ocean'];
    if (!in_array($deliveryMode, $validDeliveryModes)) {
        ApiResponse::error('Invalid delivery mode');
    }
    
    $validPriorities = ['Low', 'Medium', 'High', 'Urgent'];
    if (!in_array($priority, $validPriorities)) {
        ApiResponse::error('Invalid priority level');
    }
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check if customer exists
    $customerQuery = "SELECT CustomerID, FirstName, LastName FROM Customer WHERE CustomerID = ? AND IsActive = 1";
    $customerStmt = $database->execute($customerQuery, [$customerID]);
    $customer = $customerStmt->fetch();
    
    if (!$customer) {
        ApiResponse::error('Customer not found or inactive', 404);
    }
    
    // Generate tracking number
    $trackingNumber = Database::generateTrackingNumber();
    
    // Ensure tracking number is unique
    $trackingCheckQuery = "SELECT TrackingNumber FROM Shipment WHERE TrackingNumber = ?";
    $attempts = 0;
    while ($attempts < 10) {
        $trackingStmt = $database->execute($trackingCheckQuery, [$trackingNumber]);
        if ($trackingStmt->rowCount() == 0) {
            break; // Unique tracking number found
        }
        $trackingNumber = Database::generateTrackingNumber();
        $attempts++;
    }
    
    if ($attempts >= 10) {
        ApiResponse::serverError('Unable to generate unique tracking number');
    }
    
    // Calculate shipping cost
    $shippingCost = Database::calculateShippingCost($weight, $deliveryMode, $shipmentType);
    
    // Calculate estimated delivery date
    $estimatedDays = 7; // Default
    switch ($shipmentType) {
        case 'Express':
            $estimatedDays = ($deliveryMode === 'Air') ? 3 : 5;
            break;
        case 'Standard':
            $estimatedDays = ($deliveryMode === 'Air') ? 5 : 10;
            break;
        case 'Special Handling':
            $estimatedDays = ($deliveryMode === 'Air') ? 4 : 7;
            break;
    }
    
    $estimatedDeliveryDate = date('Y-m-d', strtotime("+$estimatedDays days"));
    
    // Begin transaction
    $database->beginTransaction();
    
    try {
        // Insert shipment
        $shipmentQuery = "INSERT INTO Shipment (
            TrackingNumber, CustomerID, SenderName, SenderAddress, SenderPhone,
            RecipientName, RecipientAddress, RecipientPhone, ShipmentType, DeliveryMode,
            Weight, Dimensions, DeclaredValue, ShippingCost, Priority, SpecialInstructions,
            EstimatedDeliveryDate, Status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Pending')";
        
        $shipmentParams = [
            $trackingNumber, $customerID, $senderName, $senderAddress, $senderPhone,
            $recipientName, $recipientAddress, $recipientPhone, $shipmentType, $deliveryMode,
            $weight, $dimensions, $declaredValue, $shippingCost, $priority, $specialInstructions,
            $estimatedDeliveryDate
        ];
        
        $database->execute($shipmentQuery, $shipmentParams);
        $shipmentID = $database->lastInsertId();
        
        // Create initial tracking event
        $eventQuery = "INSERT INTO TrackingEvent (ShipmentID, EventType, EventDescription, Location, EventDateTime, CreatedBy)
                       VALUES (?, 'Created', 'Shipment information received and processed', 'Lusaka, Zambia', NOW(), ?)";
        
        $database->execute($eventQuery, [$shipmentID, $userID]);
        
        // Create package record if package details provided
        if (!empty($input['package_description'])) {
            $packageQuery = "INSERT INTO Package (ShipmentID, PackageNumber, Description, Weight, Dimensions, Value, Contents, IsFragile, RequiresSignature)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $packageNumber = $trackingNumber . '-P1';
            $packageDescription = Database::sanitize($input['package_description']);
            $packageContents = Database::sanitize($input['package_contents'] ?? '');
            $isFragile = isset($input['is_fragile']) ? (bool)$input['is_fragile'] : false;
            $requiresSignature = isset($input['requires_signature']) ? (bool)$input['requires_signature'] : true;
            
            $packageParams = [
                $shipmentID, $packageNumber, $packageDescription, $weight, $dimensions,
                $declaredValue, $packageContents, $isFragile, $requiresSignature
            ];
            
            $database->execute($packageQuery, $packageParams);
        }
        
        // Create notification for customer
        $notificationQuery = "INSERT INTO Notifications (UserID, ShipmentID, Type, Title, Message, Priority)
                             VALUES (?, ?, 'Shipment Update', 'Shipment Created', ?, 'Medium')";
        
        $notificationMessage = "Your shipment {$trackingNumber} has been created and is being processed. You will receive updates as your package moves through our network.";
        $database->execute($notificationQuery, [$customerID, $shipmentID, $notificationMessage]);
        
        // Log analytics
        $database->logAnalytics($userID, 'CREATE_SHIPMENT', 'Shipments', [
            'tracking_number' => $trackingNumber,
            'customer_id' => $customerID,
            'shipment_type' => $shipmentType,
            'delivery_mode' => $deliveryMode,
            'weight' => $weight,
            'shipping_cost' => $shippingCost
        ]);
        
        // Commit transaction
        $database->commit();
        
        // Prepare response data
        $responseData = [
            'shipment' => [
                'id' => $shipmentID,
                'tracking_number' => $trackingNumber,
                'customer_id' => $customerID,
                'customer_name' => $customer['FirstName'] . ' ' . $customer['LastName'],
                'sender_name' => $senderName,
                'recipient_name' => $recipientName,
                'shipment_type' => $shipmentType,
                'delivery_mode' => $deliveryMode,
                'weight' => $weight,
                'shipping_cost' => $shippingCost,
                'priority' => $priority,
                'status' => 'Pending',
                'estimated_delivery_date' => $estimatedDeliveryDate,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        ApiResponse::success($responseData, 'Shipment created successfully', 201);
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Create shipment error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            SessionManager::getUserId(),
            'CREATE_SHIPMENT_ERROR',
            $e->getMessage(),
            $e->getTraceAsString(),
            $input ?? []
        );
    }
    
    ApiResponse::serverError('An error occurred while creating the shipment');
}
?>
