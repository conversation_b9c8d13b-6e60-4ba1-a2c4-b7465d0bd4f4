-- ZamShipment Courier Services Database Schema
-- Created for LIS3010 Assignment
-- Database: courier_db

-- Create database
CREATE DATABASE IF NOT EXISTS courier_db;
USE courier_db;

-- Drop tables if they exist (for clean setup)
DROP TABLE IF EXISTS NewsletterSubscriptions;
DROP TABLE IF EXISTS ErrorLog;
DROP TABLE IF EXISTS Analytics;
DROP TABLE IF EXISTS Notifications;
DROP TABLE IF EXISTS Delivery;
DROP TABLE IF EXISTS TrackingEvent;
DROP TABLE IF EXISTS Package;
DROP TABLE IF EXISTS PickUp;
DROP TABLE IF EXISTS Shipment;
DROP TABLE IF EXISTS Company;
DROP TABLE IF EXISTS Customer;
DROP TABLE IF EXISTS UserPermissions;
DROP TABLE IF EXISTS Permissions;
DROP TABLE IF EXISTS Users;
DROP TABLE IF EXISTS SystemSettings;

-- 1. SystemSettings Table
CREATE TABLE SystemSettings (
    SettingID INT PRIMARY KEY AUTO_INCREMENT,
    SettingName VARCHAR(100) NOT NULL UNIQUE,
    SettingValue TEXT,
    Description TEXT,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Users Table (Super Admin, Sub-Admin, Customer Service, Logistics)
CREATE TABLE Users (
    UserID INT PRIMARY KEY AUTO_INCREMENT,
    Username VARCHAR(50) NOT NULL UNIQUE,
    Email VARCHAR(100) NOT NULL UNIQUE,
    PasswordHash VARCHAR(255) NOT NULL,
    FirstName VARCHAR(50) NOT NULL,
    LastName VARCHAR(50) NOT NULL,
    OtherName VARCHAR(50) NULL,
    Phone VARCHAR(20),
    Role ENUM('SuperAdmin', 'SubAdmin', 'CustomerService', 'Logistics') NOT NULL,
    IsActive BOOLEAN DEFAULT TRUE,
    LastLogin TIMESTAMP NULL,
    TwoFactorEnabled BOOLEAN DEFAULT FALSE,
    TwoFactorSecret VARCHAR(32),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (Username),
    INDEX idx_email (Email),
    INDEX idx_role (Role)
);

-- 3. Permissions Table
CREATE TABLE Permissions (
    PermissionID INT PRIMARY KEY AUTO_INCREMENT,
    PermissionName VARCHAR(100) NOT NULL UNIQUE,
    Description TEXT,
    Module VARCHAR(50) NOT NULL, -- e.g., 'Customers', 'Shipments', 'Reports'
    Action VARCHAR(50) NOT NULL, -- e.g., 'View', 'Create', 'Edit', 'Delete'
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. UserPermissions Table (Many-to-Many relationship)
CREATE TABLE UserPermissions (
    UserPermissionID INT PRIMARY KEY AUTO_INCREMENT,
    UserID INT NOT NULL,
    PermissionID INT NOT NULL,
    GrantedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    GrantedBy INT NOT NULL,
    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE CASCADE,
    FOREIGN KEY (PermissionID) REFERENCES Permissions(PermissionID) ON DELETE CASCADE,
    FOREIGN KEY (GrantedBy) REFERENCES Users(UserID),
    UNIQUE KEY unique_user_permission (UserID, PermissionID)
);

-- 5. Customer Table
CREATE TABLE Customer (
    CustomerID INT PRIMARY KEY AUTO_INCREMENT,
    FirstName VARCHAR(50) NOT NULL,
    LastName VARCHAR(50) NOT NULL,
    OtherName VARCHAR(50) NULL,
    Email VARCHAR(100) NOT NULL UNIQUE,
    Phone VARCHAR(20) NOT NULL,
    Address TEXT NOT NULL,
    City VARCHAR(50) NOT NULL,
    State VARCHAR(50) NOT NULL,
    PostalCode VARCHAR(20) NOT NULL,
    Country VARCHAR(50) NOT NULL DEFAULT 'Zambia',
    DateOfBirth DATE,
    IsActive BOOLEAN DEFAULT TRUE,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (Email),
    INDEX idx_phone (Phone),
    INDEX idx_name (FirstName, LastName)
);

-- 6. Company Table (Optional for business customers)
CREATE TABLE Company (
    CompanyID INT PRIMARY KEY AUTO_INCREMENT,
    CustomerID INT NOT NULL,
    CompanyName VARCHAR(100) NOT NULL,
    RegistrationNumber VARCHAR(50),
    TaxID VARCHAR(50),
    Industry VARCHAR(50),
    Website VARCHAR(100),
    BillingAddress TEXT,
    ContactPerson VARCHAR(100),
    ContactEmail VARCHAR(100),
    ContactPhone VARCHAR(20),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (CustomerID) REFERENCES Customer(CustomerID) ON DELETE CASCADE,
    INDEX idx_company_name (CompanyName),
    INDEX idx_registration (RegistrationNumber)
);

-- 7. Shipment Table
CREATE TABLE Shipment (
    ShipmentID INT PRIMARY KEY AUTO_INCREMENT,
    TrackingNumber VARCHAR(20) NOT NULL UNIQUE,
    CustomerID INT NOT NULL,
    SenderName VARCHAR(100) NOT NULL,
    SenderAddress TEXT NOT NULL,
    SenderPhone VARCHAR(20) NOT NULL,
    RecipientName VARCHAR(100) NOT NULL,
    RecipientAddress TEXT NOT NULL,
    RecipientPhone VARCHAR(20) NOT NULL,
    ShipmentType ENUM('Express', 'Standard', 'Special Handling') NOT NULL,
    DeliveryMode ENUM('Air', 'Road', 'Rail', 'Ocean') NOT NULL,
    Weight DECIMAL(10,2) NOT NULL, -- in kg
    Dimensions VARCHAR(50), -- LxWxH in cm
    DeclaredValue DECIMAL(10,2),
    ShippingCost DECIMAL(10,2) NOT NULL,
    Status ENUM('Pending', 'Picked Up', 'In Transit', 'Out for Delivery', 'Delivered', 'Cancelled') DEFAULT 'Pending',
    Priority ENUM('Low', 'Medium', 'High', 'Urgent') DEFAULT 'Medium',
    SpecialInstructions TEXT,
    EstimatedDeliveryDate DATE,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (CustomerID) REFERENCES Customer(CustomerID),
    INDEX idx_tracking (TrackingNumber),
    INDEX idx_customer (CustomerID),
    INDEX idx_status (Status),
    INDEX idx_created_date (CreatedAt)
);

-- 8. Package Table
CREATE TABLE Package (
    PackageID INT PRIMARY KEY AUTO_INCREMENT,
    ShipmentID INT NOT NULL,
    PackageNumber VARCHAR(20) NOT NULL,
    Description TEXT,
    Weight DECIMAL(10,2) NOT NULL,
    Dimensions VARCHAR(50),
    Value DECIMAL(10,2),
    Contents TEXT,
    IsFragile BOOLEAN DEFAULT FALSE,
    RequiresSignature BOOLEAN DEFAULT FALSE,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ShipmentID) REFERENCES Shipment(ShipmentID) ON DELETE CASCADE,
    INDEX idx_shipment (ShipmentID),
    INDEX idx_package_number (PackageNumber)
);

-- 9. PickUp Table
CREATE TABLE PickUp (
    PickUpID INT PRIMARY KEY AUTO_INCREMENT,
    ShipmentID INT NOT NULL,
    ScheduledDate DATE NOT NULL,
    ScheduledTime TIME NOT NULL,
    ActualPickUpDate DATETIME,
    PickUpAddress TEXT NOT NULL,
    ContactPerson VARCHAR(100) NOT NULL,
    ContactPhone VARCHAR(20) NOT NULL,
    SpecialInstructions TEXT,
    Status ENUM('Scheduled', 'In Progress', 'Completed', 'Failed', 'Cancelled') DEFAULT 'Scheduled',
    DriverID INT,
    Notes TEXT,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (ShipmentID) REFERENCES Shipment(ShipmentID) ON DELETE CASCADE,
    FOREIGN KEY (DriverID) REFERENCES Users(UserID),
    INDEX idx_shipment (ShipmentID),
    INDEX idx_scheduled_date (ScheduledDate),
    INDEX idx_status (Status)
);

-- 10. TrackingEvent Table
CREATE TABLE TrackingEvent (
    EventID INT PRIMARY KEY AUTO_INCREMENT,
    ShipmentID INT NOT NULL,
    EventType ENUM('Created', 'Picked Up', 'In Transit', 'Arrived at Facility', 'Out for Delivery', 'Delivered', 'Exception', 'Cancelled') NOT NULL,
    EventDescription TEXT NOT NULL,
    Location VARCHAR(100),
    EventDateTime DATETIME NOT NULL,
    CreatedBy INT,
    Notes TEXT,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ShipmentID) REFERENCES Shipment(ShipmentID) ON DELETE CASCADE,
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    INDEX idx_shipment (ShipmentID),
    INDEX idx_event_type (EventType),
    INDEX idx_event_datetime (EventDateTime)
);

-- 11. Delivery Table
CREATE TABLE Delivery (
    DeliveryID INT PRIMARY KEY AUTO_INCREMENT,
    ShipmentID INT NOT NULL,
    DeliveryDate DATETIME NOT NULL,
    DeliveryAddress TEXT NOT NULL,
    RecipientName VARCHAR(100) NOT NULL,
    SignatureType ENUM('Digital', 'Image', 'Text') NOT NULL,
    SignatureData TEXT, -- Base64 encoded image or text signature
    DeliveryNotes TEXT,
    DeliveredBy INT,
    ProofOfDelivery VARCHAR(255), -- File path for delivery proof
    DeliveryStatus ENUM('Successful', 'Failed', 'Partial') DEFAULT 'Successful',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ShipmentID) REFERENCES Shipment(ShipmentID) ON DELETE CASCADE,
    FOREIGN KEY (DeliveredBy) REFERENCES Users(UserID),
    INDEX idx_shipment (ShipmentID),
    INDEX idx_delivery_date (DeliveryDate)
);

-- 12. Notifications Table
CREATE TABLE Notifications (
    NotificationID INT PRIMARY KEY AUTO_INCREMENT,
    UserID INT,
    ShipmentID INT,
    Type ENUM('Shipment Update', 'Delivery Alert', 'System Alert', 'Security Alert') NOT NULL,
    Title VARCHAR(200) NOT NULL,
    Message TEXT NOT NULL,
    IsRead BOOLEAN DEFAULT FALSE,
    Priority ENUM('Low', 'Medium', 'High', 'Critical') DEFAULT 'Medium',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ReadAt TIMESTAMP NULL,
    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE CASCADE,
    FOREIGN KEY (ShipmentID) REFERENCES Shipment(ShipmentID) ON DELETE CASCADE,
    INDEX idx_user (UserID),
    INDEX idx_shipment (ShipmentID),
    INDEX idx_is_read (IsRead),
    INDEX idx_created_at (CreatedAt)
);

-- 13. Analytics Table
CREATE TABLE Analytics (
    AnalyticsID INT PRIMARY KEY AUTO_INCREMENT,
    UserID INT,
    Action VARCHAR(100) NOT NULL,
    Module VARCHAR(50) NOT NULL,
    Details JSON,
    IPAddress VARCHAR(45),
    UserAgent TEXT,
    SessionID VARCHAR(100),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE SET NULL,
    INDEX idx_user (UserID),
    INDEX idx_action (Action),
    INDEX idx_module (Module),
    INDEX idx_created_at (CreatedAt)
);

-- 14. ErrorLog Table
CREATE TABLE ErrorLog (
    ErrorID INT PRIMARY KEY AUTO_INCREMENT,
    UserID INT,
    ErrorType VARCHAR(100) NOT NULL,
    ErrorMessage TEXT NOT NULL,
    StackTrace TEXT,
    RequestURL VARCHAR(500),
    RequestMethod VARCHAR(10),
    RequestData JSON,
    IPAddress VARCHAR(45),
    UserAgent TEXT,
    Severity ENUM('Low', 'Medium', 'High', 'Critical') DEFAULT 'Medium',
    IsResolved BOOLEAN DEFAULT FALSE,
    ResolvedBy INT,
    ResolvedAt TIMESTAMP NULL,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE SET NULL,
    FOREIGN KEY (ResolvedBy) REFERENCES Users(UserID) ON DELETE SET NULL,
    INDEX idx_user (UserID),
    INDEX idx_error_type (ErrorType),
    INDEX idx_severity (Severity),
    INDEX idx_is_resolved (IsResolved),
    INDEX idx_created_at (CreatedAt)
);

-- 15. NewsletterSubscriptions Table
CREATE TABLE NewsletterSubscriptions (
    SubscriptionID INT AUTO_INCREMENT PRIMARY KEY,
    Email VARCHAR(255) NOT NULL UNIQUE,
    FirstName VARCHAR(100),
    LastName VARCHAR(100),
    IsActive BOOLEAN DEFAULT TRUE,
    SubscribedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UnsubscribedAt TIMESTAMP NULL,
    UnsubscribeToken VARCHAR(255) UNIQUE,
    INDEX idx_email (Email),
    INDEX idx_active (IsActive)
);
