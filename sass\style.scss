$font-primary: 'Work Sans', Arial, sans-serif;



// Overrides
$grid-gutter-width: 40px !default; 
$border-radius-base:  4px !default;
$padding-base-vertical: 14px !default;

$brand-primary: #EA9215 !default;
$brand-secondary: #09C6AB !default;

$brand-white: #fff;
$brand-black: #000;
$brand-darker: #444;
$brand-gray: #ccc;
$brand-lighter: #e9e9e9;
$brand-body-color: #818892;
$brand-selection-color: #f9f6f0;
$brand-overlay-color: #4c4a8a;
$brand-bg-color: $brand-white;

$input-border-focus:  $brand-primary !default;
$form-group-margin-bottom:       30px !default;



// Mixin
@mixin translateX($translatex) {
	-moz-transform: translateX($translatex);
	-webkit-transform: translateX($translatex);
	-ms-transform: translateX($translatex);
	-o-transform: translateX($translatex);
	transform: translateX($translatex);
}
@mixin transition($transition) {
    -moz-transition:    all $transition ease;
    -o-transition:      all $transition ease;
    -webkit-transition: all $transition ease;
    -ms-transition: 		all $transition ease;
    transition:         all $transition ease;
}
@mixin inline-block() {
	display:-moz-inline-stack;
	display:inline-block;
	zoom:1;
	*display:inline;
}

@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
     -moz-border-radius: $radius;
      -ms-border-radius: $radius;
          border-radius: $radius;
}
@mixin flex() {
	display: -webkit-box;      
  	display: -moz-box;         
  	display: -ms-flexbox;      
  	display: -webkit-flex;     
  	display: flex;             
}
@mixin flexwrap() {
	flex-wrap: wrap;
	-webkit-flex-wrap: wrap; 
	-moz-flex-wrap: wrap; 
}

@font-face {
	font-family: 'icomoon';
	src:url('../fonts/icomoon/icomoon.eot?srf3rx');
	src:url('../fonts/icomoon/icomoon.eot?srf3rx#iefix') format('embedded-opentype'),
		url('../fonts/icomoon/icomoon.ttf?srf3rx') format('truetype'),
		url('../fonts/icomoon/icomoon.woff?srf3rx') format('woff'),
		url('../fonts/icomoon/icomoon.svg?srf3rx#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

@mixin icomoon() {
	
	font-family: 'icomoon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	
}

// Import 
@import 'bootstrap/mixins';
@import 'bootstrap/variables';




/* =======================================================
*
* 	Template Style 
*
* ======================================================= */

body {
	font-family: $font-primary;
	font-weight: 400;
	font-size: 16px;
	line-height: 1.7;
	color: #828282;
	background: #fff;
}
#page {
	position: relative;
	overflow-x: hidden;
	width: 100%;
	height: 100%;
	@include transition(.5s);
	.offcanvas & {
		overflow: hidden;	
		position: absolute;
		
		&:after {
			@include transition(2s);
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 101;
			background: rgba(0,0,0,.7);
			content: "";
		}
	}
}
a {
	color: $brand-primary;
	@include transition(.5s);
	&:hover, &:active, &:focus {
		color: $brand-primary;
		outline: none;
		text-decoration: none;
	}
}
p {
	margin-bottom: 20px;
}

h1, h2, h3, h4, h5, h6, figure {
	color: $brand-black;
	font-family: $font-primary;
	font-weight: 400;
	margin: 0 0 20px 0;
}
::-webkit-selection {
  color: $brand-white;
  background: $brand-primary;
}

::-moz-selection {
  color: $brand-white;
  background: $brand-primary;
}
::selection {
  color: $brand-white;
  background: $brand-primary;
}

.fh5co-nav {
	position: absolute;
	top: 0;
	margin: 0;
	padding: 0;
	width: 100%;
	padding: 0;
	z-index: 1001;
	// @media screen and (max-width: $screen-sm) {
	// 	padding: 20px 0;
	// }
	.top-menu{
		padding: 28px 0;
	}
	.top{		
		padding: 7px 0;
		margin-bottom: 0;
		.num, .fh5co-social{
			display: inline-block;
			margin: 0;
		}
		.num{
			color: rgba($brand-white,.6);
			font-size: 13px;
			padding-right: 10px;
			margin-right: 5px;
			border-right: 1px solid rgba($brand-white,.2);
			letter-spacing: 0px;
		}
		.fh5co-social{
			li{
				font-size: 14px;
				a{
					padding: 0 7px;
					i{
						font-size: 14px;
					}
				}
			}
		}
	}
	#fh5co-logo {
		font-size: 24px;
		margin: 0;
		padding: 0;
		text-transform: uppercase;
		font-weight: bold;
		font-weight: 700;
		font-family: $font-primary;
		a{
			span{
				color: $brand-primary;
			}
		}
	}
	a {
		padding: 5px 10px;
		color: $brand-white;
	}
	.menu-1 {
		@media screen and (max-width: $screen-sm ) {
			display: none;
		}
	}
	ul {
		padding: 0;
		margin: 5px 0 0 0;
		li {
			padding: 0;
			margin: 0;
			list-style: none;
			display: inline;
			a {
				font-size: 15px;
				padding: 30px 15px;
				color: rgba(255,255,255,.5);
				@include transition(.5s);
				&:hover,&:focus, &:active {
					color: rgba(255,255,255,1);
				}
			}
			&.has-dropdown {
				position: relative;
				.dropdown {
					width: 140px;
					-webkit-box-shadow: 0px 14px 33px -9px rgba(0,0,0,0.75);
					-moz-box-shadow: 0px 14px 33px -9px rgba(0,0,0,0.75);
					box-shadow: 0px 14px 33px -9px rgba(0,0,0,0.75);
					z-index: 1002;
					visibility: hidden;
					opacity: 0;
					position: absolute;
					top: 40px;
					left: 0;
					text-align: left;
					background: $brand-black;
					padding: 20px;
					@include border-radius(4px);
					@include transition(.0s);
					&:before {
						bottom: 100%;
						left: 40px;
						border: solid transparent;
						content: " ";
						height: 0;
						width: 0;
						position: absolute;
						pointer-events: none;
						border-bottom-color: #000;
						border-width: 8px;
						margin-left: -8px;
					}
					
					li {
						display: block;
						margin-bottom: 7px;
						&:last-child {
							margin-bottom: 0;
						}
						a {
							padding: 2px 0;
							display: block;
							color: lighten($brand-black, 60%);
							line-height: 1.2;
							text-transform: none;
							font-size: 13px;
							letter-spacing: 0;
							&:hover {
								color: $brand-white;
							}
						}
					}
				}
				&:hover, &:focus {
					a {
						color: $brand-white;
					}
					.dropdown {
						// visibility: visible;
						// opacity: 1;
					}
				}
			}
			&.btn-cta {
				a {
					padding: 30px 0px !important;
					color: $brand-white;
					span {
						background: rgba($brand-white,.2);
						padding: 4px 10px;
						@include inline-block;
						@include transition(.3s);
						@include border-radius(100px);
					}
					&:hover {
						span {
							-webkit-box-shadow: 0px 14px 20px -9px rgba(0,0,0,0.75);
							-moz-box-shadow: 0px 14px 20px -9px rgba(0,0,0,0.75);
							box-shadow: 0px 14px 20px -9px rgba(0,0,0,0.75);
						}
					}
				}
			}
			&.active {
				> a {
					color: $brand-white!important;
				}
			}
		}
	}
}
#fh5co-header,
#fh5co-counter,
.fh5co-bg {
	background-size: cover;
	background-position: top center;
	background-repeat: no-repeat;
	position: relative;
}
.fh5co-bg {
	background-size: cover;
	background-position: center center;
	position: relative;
	width: 100%;
	float: left;
}

.fh5co-video {
	// height: 450px;
	overflow: hidden;
	// @include border-radius(7px);
	@media screen and (max-width: $screen-md){
		height: 450px;
	}
	a {
		z-index: 1001;
		position: absolute;
		top: 50%;
		left: 50%;
		margin-top: -45px;
		margin-left: -45px;
		width: 90px;
		height: 90px;
		display: table;
		text-align: center;
		background: $brand-white;
		
		-webkit-box-shadow: 0px 14px 30px -15px rgba(0,0,0,0.75);
		-moz-box-shadow: 0px 14px 30px -15px rgba(0,0,0,0.75);
		box-shadow: 0px 14px 30px -15px rgba(0,0,0,0.75);
		@include border-radius(50%);
		
		i {
			text-align: center;
			display: table-cell;
			vertical-align: middle;
			font-size: 40px;

		}
	}
	.overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, .5);
		@include transition(.5s);
	}
	&:hover {
		.overlay {
			background: rgba(0, 0, 0, .7);		
		}
		a {
			// position: relative;
			-webkit-transform: scale(1.2);
			-moz-transform: scale(1.2);
			-ms-transform: scale(1.2);
			-o-transform: scale(1.2);
			transform: scale(1.2);
			
		}
	}
}
.fh5co-cover {
	height: 800px;

	background-size: cover;
	background-position: top center;
	background-repeat: no-repeat;
	position: relative;
	float: left;
	width: 100%;
	.overlay {
		z-index: 0;
		position: absolute;
		bottom: 0;
		top: 0;
		left: 0;
		right: 0;
		background: rgba($brand-black,.4);
	}
	> .fh5co-container {
		position: relative;
		z-index: 10;
	}
	@media screen and (max-width: $screen-sm) {
		height: 600px;
	}
	.display-t,
	.display-tc {
		z-index: 9;
		height: 700px;
		display: table;
		width: 100%;
		margin-top: 100px;
		@media screen and (max-width: $screen-sm) {
			height: 600px;
		}
	}	

	&.fh5co-cover-sm {
		height: 600px;
		@media screen and (max-width: $screen-sm) {
			height: 400px;
		}
		.display-t,
		.display-tc {
			height: 600px;
			display: table;
			width: 100%;
			@media screen and (max-width: $screen-sm) {
				height: 400px;
			}
		}	
	}
}




#fh5co-header,
#fh5co-counter,
.fh5co-cover {
	.display-tc {
		display: table-cell!important;
		vertical-align: middle;
		h1, h2 {
			margin: 0;
			padding: 0;
			color: rgba(255,255,255,1);
		}
		h1 {
			margin-bottom: 20px;
			font-size: 54px;
			line-height: 1.3;
			font-weight: 300;
			@media screen and (max-width: $screen-sm) {
				font-size: 30px;
			}
		}
		h2 {
			font-size: 20px;
			line-height: 1.5;
			margin-bottom: 30px;
		}
		.btn {
			padding: 15px 30px;
			// background: $brand-primary!important;
			color: $brand-white;
			border: none!important;
			font-size: 18px;
			&.btn-video{
				background: rgba($brand-secondary,.8);
				border: none;
				&:hover, &:focus{
					background: rgba($brand-secondary,.9) !important;
					color: $brand-white !important;
				}
			}
			&:hover, &:focus{
				color: $brand-white !important;
			}
			&.btn-learn{
				background: rgba($brand-primary,.8);
				border: none;
				&:hover, &:focus{
					background: rgba($brand-primary,.9) !important;
					color: $brand-white !important;
				}
			}
			&:hover {
				background: $brand-primary!important;
				-webkit-box-shadow: 0px 14px 30px -15px rgba(0,0,0,0.75)!important;
				-moz-box-shadow: 0px 14px 30px -15px rgba(0,0,0,0.75)!important;
				box-shadow: 0px 14px 30px -15px rgba(0,0,0,0.75)!important;
			}
		}
		.form-inline {
			.form-group {
				width: 100%!important;
				margin-bottom: 10px;
				.form-control {
					width: 100%;
					background: $brand-white;
					border: none;
				}
			}
		}
	}
}


.fh5co-bg-section{
	background: rgba($brand-black,.05);
}

#fh5co-explore,
#fh5co-pricing,
#fh5co-testimonial,
#fh5co-started,
#fh5co-project,
#fh5co-blog,
#fh5co-contact,
#fh5co-footer{
	padding: 7em 0;
	clear: both;
	@media screen and (max-width: $screen-sm) {
		padding: 3em 0;
	}
}

#fh5co-counter{
	padding: 3em 0;
	clear: both;
}

#fh5co-steps{
	clear: both;
	padding-bottom: 7em;
}


// Counters
.fh5co-counters {
	padding: 3em 0;
	background-size: cover;
	background-attachment: fixed;
	background-position: center center;
	.counter-wrap{
		border: 1px solid red !important;
	}
	.fh5co-counter {
		font-size: 44px;
		display: block;
		color: rgba($brand-black,.7);
		font-family: $font-primary;
		width: 100%;
		font-weight: 400;
		margin-bottom: .3em;
	}
	.fh5co-counter-label {
		color: rgba($brand-black,.5);
		text-transform: uppercase;
		font-size: 14px;
		letter-spacing: 5px;
		margin-bottom: 2em;
		display: block;
	}
}

#fh5co-explore{
	.fh5co-explore1{
		margin-bottom: 7em;
	}
}


.mt{
	margin-top: 70px;

	h4{
		position: relative;
		padding-left: 40px;
		font-size: 20px;
		i{
			position: absolute;
			left: 0;
			top: 0;
			color: $brand-primary;
		}
	}

	> div{
		margin-bottom: 40px;
	}

	.list-nav{
		margin: 50px 0 30px 0;
		padding: 0;
		li{
			list-style: none;
			margin: 0;
			padding: 0;
			font-size: 16px;
			padding-left: 30px;
			margin-bottom: 10px;
			position: relative;

			i{
				position: absolute;
				left: 0;
				top: 0;
				font-size: 18px;
				color: $brand-primary;
			}	
		}
	}
}



.fh5co-social-icons {
	margin: 0;
	padding: 0;
	li {
		margin: 0;
		padding: 0;
		list-style: none;
		@include inline-block;
		a {
			@include inline-block;
			color: $brand-primary;
			padding-left: 10px;
			padding-right: 10px;
			i {
				font-size: 20px;
			}
		}
	}
}

.fh5co-contact-info {
	ul {
		padding: 0;
		margin: 0;
		li {
			padding: 0 0 0 40px;
			margin: 0 0 30px 0;
			list-style: none;
			position: relative;
			&:before {
				color: $brand-primary;
				position: absolute;
				left: 0;
				top: .05em;
				@include icomoon;
			}
			&.address {
				&:before {
					font-size: 30px;	
					content: "\e9d1";
				}
			}
			&.phone {
				&:before {
					font-size: 23px;
					content: "\e9f4";
				}
			}
			&.email {
				&:before {
					font-size: 23px;
					content: "\e9da";
				}
			}
			&.url {
				&:before {
					font-size: 23px;
					content: "\e9af";
				}
			}
		}
	}
}

.proj-bottom{
	padding-bottom: 4em;
}
.fh5co-project {
	margin-bottom: 30px;
	> a {
		display: block;
		color: $brand-black;
		position: relative;
		bottom: 0;
		overflow: hidden;
		@include transition(.5s);
		img {
			position: relative;
			@include transition(.5s);
		}
		&:after {
			opacity: 0;
			visibility: hidden;
			content: "";
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			top: 0;
			-webkit-box-shadow: inset 0px -34px 98px 8px rgba(0,0,0,0.75);
			-moz-box-shadow: inset 0px -34px 98px 8px rgba(0,0,0,0.75);
			-ms-box-shadow: inset 0px -34px 98px 8px rgba(0,0,0,0.75);
			-o-box-shadow: inset 0px -34px 98px 8px rgba(0,0,0,0.75);
			box-shadow: inset 0px -34px 98px 8px rgba(0,0,0,0.75);
			z-index: 8;
			@include transition(.5s)
		}
		h3, span {
			z-index: 12;
			position: absolute;
			right: 20px;
			left: 20px;
			bottom: 50px;
			color: $brand-white;
			margin: 0;
			padding: 0;
			opacity: 1;
			font-size: 30px;
			font-weight: 300;
			visibility: visible;
			@include transition(.3s);
		}
		span{
				font-size: 14px;
				bottom: 20px;
			}
		&:hover {
			-webkit-box-shadow: 0px 18px 71px -10px rgba(0,0,0,0.75);
			-moz-box-shadow: 0px 18px 71px -10px rgba(0,0,0,0.75);
			box-shadow: 0px 18px 71px -10px rgba(0,0,0,0.75);

			&:after {
				opacity: 1;
				visibility: visible;
				@media screen and (max-width: $screen-sm ) {
					opacity: 0;
					visibility: hidden;
				}
			}
			h3 ,span {
				opacity: 1;
				bottom: 55px;
			}
			span{
				bottom: 15px;
			}
			img {
				-webkit-transform: scale(1.1);
				-moz-transform: scale(1.1);
				-o-transform: scale(1.1);
				transform: scale(1.1);

				@media screen and (max-width: $screen-sm ) {
					
					-webkit-transform: scale(1.0);
					-moz-transform: scale(1.0);
					-o-transform: scale(1.0);
					transform: scale(1.0);
				}
			}
		}
	}
}
.features{
	margin-bottom: 3em;
	width: 100%;
	float: left;
}

.fh5co-heading {
	margin-bottom: 5em;
	&.fh5co-heading-sm {
		margin-bottom: 2em;
	}
	h2 {
		font-size: 32px;
		margin-bottom: 20px;
		line-height: 1.5;
		color: $brand-black;
	}
	p {
		font-size: 18px;
		line-height: 1.5;
		color: #828282;
	}
	span{
		display: block;
		margin-bottom: 10px;
		text-transform: uppercase;
		font-size: 12px;
		letter-spacing: 2px;
	}
}

#fh5co-testimonial {
	background: #efefef;
	.testimony-slide{
		// display: none;
		text-align: center;
		span{
			font-size: 12px;
			text-transform: uppercase;
			letter-spacing: 2px;
			font-weight: 700;
			display: block;
		}

		figure{
			margin-bottom: 10px;
			@include inline-block;
			
			img {
				width: 100px;
				@include border-radius(50%);
			}
		}

		blockquote{
			border: none;
			margin: 30px auto;
			width: 70%;
			position: relative;
			padding: 0;
			@media screen and (max-width: $screen-md) {
				width: 100%;
			}
			
		}

	}

	.arrow-thumb{
		position: absolute;
		top: 40%;
		display: block;
		width: 100%;

		a{
			font-size: 32px;
			color: #dadada;

			&:hover, &:focus, &:active{
				text-decoration: none;
			}
		}

	}
}


//STEPS
.bs-wizard {margin-top: 40px;}

/*Form Wizard*/
.bs-wizard {
	border-bottom: solid 1px #e0e0e0; padding: 0 0 10px 0;
}
.bs-wizard > .bs-wizard-step {padding: 0; position: relative;}
.bs-wizard > .bs-wizard-step .bs-wizard-stepnum { font-size: 16px; margin-bottom: 10px;}
.bs-wizard > .bs-wizard-step .bs-wizard-info {color: #999; font-size: 14px; padding: 20px;}
.bs-wizard > .bs-wizard-step > .bs-wizard-dot {position: absolute; width: 30px; height: 30px; display: block; background: #fbe8aa; top: 45px; left: 50%; margin-top: -15px; margin-left: -15px; border-radius: 50%;} 
.bs-wizard > .bs-wizard-step > .bs-wizard-dot:after {content: ' '; width: 14px; height: 14px; background: #fbbd19; border-radius: 50px; position: absolute; top: 8px; left: 8px; } 
.bs-wizard > .bs-wizard-step > .progress {position: relative; border-radius: 0px; height: 8px; box-shadow: none; margin: 22px 0;}
.bs-wizard > .bs-wizard-step > .progress > .progress-bar {width:0px; box-shadow: none; background: #fbe8aa;}
.bs-wizard > .bs-wizard-step.complete > .progress > .progress-bar {width:100%;}
.bs-wizard > .bs-wizard-step.active > .progress > .progress-bar {width:50%;}
.bs-wizard > .bs-wizard-step:first-child.active > .progress > .progress-bar {width:0%;}
.bs-wizard > .bs-wizard-step:last-child.active > .progress > .progress-bar {width: 100%;}
.bs-wizard > .bs-wizard-step.disabled > .bs-wizard-dot {background-color: #f5f5f5;}
.bs-wizard > .bs-wizard-step.disabled > .bs-wizard-dot:after {opacity: 0;}
.bs-wizard > .bs-wizard-step:first-child  > .progress {left: 50%; width: 50%;}
.bs-wizard > .bs-wizard-step:last-child  > .progress {width: 50%;}
.bs-wizard > .bs-wizard-step.disabled a.bs-wizard-dot{ pointer-events: none; }

#fh5co-started {
	background-size: cover;
	background-position: top center;
	background-repeat: no-repeat;
	position: relative;
	float: left;
	width: 100%;
	.overlay {
		z-index: 0;
		position: absolute;
		bottom: 0;
		top: 0;
		left: 0;
		right: 0;
		background: rgba($brand-black,.7);
	}
	
	.fh5co-heading {
		h2 {
			color: $brand-white;
			margin-bottom: 20px !important;
		}
		p{
			color: rgba($brand-white,.5);
		}
	}

	.form-control {
		background: rgba(255,255,255,.2);
		border: none!important;
		color: $brand-white;
		font-size: 16px!important;
		width: 100%;


		&::-webkit-input-placeholder {
		   color: $brand-white;
		}

		&:-moz-placeholder { /* Firefox 18- */
		   color: $brand-white;  
		}

		&::-moz-placeholder {  /* Firefox 19+ */
		   color: $brand-white;  
		}

		&:-ms-input-placeholder {  
		   color: $brand-white;  
		}
		@include transition(.5s);
		&:focus {
			background: rgba(255,255,255,.3);
		}

	}
	.btn {
		height: 54px;
		border: none!important;
		background: $brand-primary;
		color: $brand-white;
		font-size: 16px;
		text-transform: uppercase;
		font-weight: 400;
		padding-left: 50px;
		padding-right: 50px;
	}
	.form-inline {
		.form-group {
			width: 100%!important;
			margin-bottom: 10px;
			.form-control {
				width: 100%;
			}
		}
	}
}


// BLOG
.fh5co-blog{
	margin-bottom: 60px;
	> a{
		display: block;
		position: relative;
		@include transition(.5s);
		img{
			width: 100%;
		}

	}
	@media screen and (max-width: $screen-sm) {
		width: 100%;
	}
	.blog-text {
		margin-bottom: 30px;
		position: relative;
		background: $brand-white;
		width: 100%;
		padding: 30px;
		float: left;

		-webkit-box-shadow: 0px 10px 20px -12px rgba(0,0,0,0.18);
		-moz-box-shadow: 0px 10px 20px -12px rgba(0,0,0,0.18);
		box-shadow: 0px 10px 20px -12px rgba(0,0,0,0.18);

		span{
			display: inline-block;
			margin-bottom: 20px;
			&.comment{
				float: right;
				a{
					color: rgba($brand-black,.3);
					i{
						color: $brand-primary;
						padding-left: 7px;
					}
				}
			}
		}

		h3{
			font-size: 20px;
			margin-bottom: 20px;
			line-height: 1.5;
			a{
				color: rgba($brand-black,1);
			}
		}
	}
}


.pricing{
	display: block;
	float: left;
	margin-bottom: 30px;
	width: 100%;
}
.price-box {
	width: 100%;
	text-align: center;
	padding: 30px;
	background: rgba($brand-black,.05);
	@include border-radius(5px);
	margin-bottom: 40px;
	position: relative;
	

	&.popular{
		.btn-select-plan{
	    	background: $brand-primary;
	   }
	   .price {
	   	color: $brand-primary;
	   }
	}

    .btn-select-plan{
    	padding: 10px 20px;
    	background: $brand-secondary;
    	color: $brand-white;

    }
    .classes{
    	padding: 0;
    	li{
    		display: block;
    		width: 100%;
    		list-style: none;
    		margin: 0;
    		font-size: 16px;
    		padding: 8px 10px;
    		
    		&.color{
    			background: rgba($brand-black,.04);
    		}
    	}
    }
}
.pricing-plan {
	margin: 0 0 50px 0;
	padding: 0;
	font-size: 13px;
	letter-spacing: 2px;
	text-transform: uppercase;
	font-weight: 700;
	color: #888f94;

	&.pricing-plan-offer{
		margin-bottom: 24px;
	}

	span{
		display: block;
		margin-top: 10px;
		margin-bottom: 0;
		color: #d5d8db;
	}
}
.price {
	font-size: 72px;
	color: $brand-black;
	line-height: 50px;
	.currency {
		font-size: 30px;
		top: -0.9em;
		padding-right: 10px;
	}
	small {
		font-size: 13px;
		display: block;
		text-transform: uppercase;
		color: #888f94;
	}
}

#fh5co-footer {
	background: #efefef;
	.fh5co-footer-links {
		padding: 0;
		margin: 0;	
		li {
			padding: 0;
			margin: 0;
			list-style: none;
			a {
				color: rgba($brand-black,.5);
				text-decoration: none;
				&:hover {
					text-decoration: underline;
				}
			}
		}
	}
	.fh5co-widget {
		margin-bottom: 30px;
		@media screen and (max-width: $screen-sm){
			text-align: left;
		}
		h3 {
			margin-bottom: 15px;
			font-weight: bold;
			font-size: 15px;
			letter-spacing: 2px;
			text-transform: uppercase;
		}
	}

	.copyright {
		.block {
			display: block;
		}
	}
}

// Map
#map {
	width: 100%;
	height: 500px;
	position: relative;
	@media screen and (max-width: $screen-sm) {
		height: 200px;
	}
}


// off canvas
#fh5co-offcanvas {
	position: absolute;
	z-index: 1901;
	width: 270px;
	background: lighten($brand-black, 0%);
	top: 0;
	right: 0;
	top: 0;
	bottom: 0;
	padding: 75px 40px 40px 40px;
	overflow-y: auto;
	display: none;
	@include translateX(270px);
	@include transition(.5s);
	@media screen and(max-width: $screen-sm){
		display: block;
	}
	.offcanvas & {
		@include translateX(0px);
	}
	a {
		color: rgba(255,255,255,.5);
		&:hover {
			color: rgba(255,255,255,.8);
		}
	}
	ul {
		padding: 0;
		margin: 0;
		li {
			padding: 0;
			margin: 0;
			list-style: none;
			> ul {
				padding-left: 20px;
				display: none;
			}
			&.offcanvas-has-dropdown {
				> a {
					display: block;
					position: relative;
					&:after {
						position: absolute;
						right: 0px;
						@include icomoon;
						content: "\e921";
						font-size: 20px;
						color: rgba(255,255,255,.2);
						@include transition(.5s);
					}
				}
				&.active {
					a {
						&:after {
							-webkit-transform: rotate(-180deg);
							-moz-transform: rotate(-180deg);
							-ms-transform: rotate(-180deg);
							-o-transform: rotate(-180deg);
							transform: rotate(-180deg);
						}
					}
				}
			}
		}
	}
}

.uppercase {
	font-size: 14px;
	color: $brand-black;
	margin-bottom: 10px;
	font-weight: 700;
	text-transform: uppercase;
}
.gototop {
	position: fixed;
	bottom: 20px;
	right: 20px;
	z-index: 999;
	opacity: 0;
	visibility: hidden;
	@include transition(.5s);
	&.active {
		opacity: 1;
		visibility: visible;
	}
	a {
		width: 50px;
		height: 50px;
		display: table;
		background: rgba(0,0,0,.5);
		color: $brand-white;
		text-align: center;
		@include border-radius(4px);
		i {
			height: 50px;
			display: table-cell;
			vertical-align: middle;

		}
		&:hover, &:active, &:focus {
			text-decoration: none;
			outline: none;
		}
	}	
}



// Burger Menu
.fh5co-nav-toggle {
  width:25px;
  height:25px;
  cursor: pointer;
  text-decoration: none;
  &.active i {
		&::before, &::after {
			background: $brand-darker;
		}
  }
  &:hover, &:focus, &:active {
  	outline: none;
  	border-bottom: none!important;
  }
  i {
  	position: relative;
	  display: inline-block;
	  width: 25px;
	  height: 2px;
	  color: #252525;
	  font:bold 14px/.4 Helvetica;
	  text-transform: uppercase;
	  text-indent:-55px;
	  background: #252525;
	  transition: all .2s ease-out;
		 &::before, &::after {
	  	content:'';
		  width: 25px;
		  height: 2px;
		  background: #252525;
		  position: absolute;
		  left:0;
		  transition:all .2s ease-out;
	  }
  }
  &.fh5co-nav-white {
  	> i {
  		color:$brand-white;
  		background: $brand-white;
  		&::before, &::after {
  			background: $brand-white;
  		}
  	}
  }
}

.fh5co-nav-toggle i::before {
  top: -7px;
}
.fh5co-nav-toggle i::after {
  bottom: -7px;
}
.fh5co-nav-toggle:hover i::before {
  top: -10px;
}
.fh5co-nav-toggle:hover i::after {
  bottom: -10px;
}
.fh5co-nav-toggle.active i {
	background: transparent;
}
.fh5co-nav-toggle.active i::before {
  top:0;
  -webkit-transform: rotateZ(45deg);
     -moz-transform: rotateZ(45deg);
      -ms-transform: rotateZ(45deg);
       -o-transform: rotateZ(45deg);
          transform: rotateZ(45deg);
}
.fh5co-nav-toggle.active i::after {
  bottom:0;
  -webkit-transform: rotateZ(-45deg);
     -moz-transform: rotateZ(-45deg);
      -ms-transform: rotateZ(-45deg);
       -o-transform: rotateZ(-45deg);
          transform: rotateZ(-45deg);
}
.fh5co-nav-toggle {
  position: absolute;
  right: 0px;
  top: 65px;
  z-index: 21;
  padding: 6px 0 0 0;
  display: block;
  margin: 0 auto;
  display: none;
  // background: #f86942;
  height: 44px;
  width: 44px;
  z-index: 2001;
  border-bottom: none!important;
  @media screen and (max-width: $screen-sm) {
  	display: block;
  }
}

// Buttons
.btn {
	margin-right: 4px;
	margin-bottom: 4px;
	font-family: $font-primary;
	font-size: 16px;
	font-weight: 400;
	@include border-radius(30px);
	@include transition(.5s);
	padding: 8px 20px;
	&.btn-md {
		padding: 8px 20px!important;
	}
	&.btn-lg {
		padding: 18px 36px!important;
	}
	&:hover, &:active, &:focus {
		box-shadow: none!important;
		outline: none!important;
	}
}
.btn-primary {
	background: $brand-primary;
	color: $brand-white;
	border: 2px solid $brand-primary;
	&:hover, &:focus, &:active {
		background: lighten($brand-primary, 5%)!important;
		border-color: lighten($brand-primary, 5%)!important;
	}
	&.btn-outline {
		background: transparent;
		color: $brand-primary;
		border: 2px solid $brand-primary;
		&:hover, &:focus, &:active {
			background: $brand-primary;
			color: $brand-white;
		}
	}
}
.btn-success {
	background: $brand-success;
	color: $brand-white;
	border: 2px solid $brand-success;
	&:hover, &:focus, &:active {
		background: darken($brand-success, 5%)!important;
		border-color: darken($brand-success, 5%)!important;
	}
	&.btn-outline {
		background: transparent;
		color: $brand-success;
		border: 2px solid $brand-success;
		&:hover, &:focus, &:active {
			background: $brand-success;
			color: $brand-white;
		}
	}
}
.btn-info {
	background: $brand-info;
	color: $brand-white;
	border: 2px solid $brand-info;
	&:hover, &:focus, &:active {
		background: darken($brand-info, 5%)!important;
		border-color: darken($brand-info, 5%)!important;
	}
	&.btn-outline {
		background: transparent;
		color: $brand-info;
		border: 2px solid $brand-info;
		&:hover, &:focus, &:active {
			background: $brand-info;
			color: $brand-white;
		}
	}
}
.btn-warning {
	background: $brand-warning;
	color: $brand-white;
	border: 2px solid $brand-warning;
	&:hover, &:focus, &:active {
		background: darken($brand-warning, 5%)!important;
		border-color: darken($brand-warning, 5%)!important;
	}
	&.btn-outline {
		background: transparent;
		color: $brand-warning;
		border: 2px solid $brand-warning;
		&:hover, &:focus, &:active {
			background: $brand-warning;
			color: $brand-white;
		}
	}
}
.btn-danger {
	background: $brand-danger;
	color: $brand-white;
	border: 2px solid $brand-danger;
	&:hover, &:focus, &:active {
		background: darken($brand-danger, 5%)!important;
		border-color: darken($brand-danger, 5%)!important;
	}
	&.btn-outline {
		background: transparent;
		color: $brand-danger;
		border: 2px solid $brand-danger;
		&:hover, &:focus, &:active {
			background: $brand-danger;
			color: $brand-white;
		}
	}
}

.btn-outline {
	background: none;
	border: 2px solid lighten($brand-black, 50%);
	font-size: 16px;
	@include transition(.3s);
	&:hover, &:focus, &:active {
		box-shadow: none;
	}
}

.btn.with-arrow {
	position: relative;
	@include transition(.3s);
	i {
		visibility: hidden;
		opacity: 0;
		position: absolute;
		right: 0px;
		top: 50%;
		margin-top: -8px;
		@include transition(.2s);
	}
	&:hover {
		padding-right: 50px;
		i {
			color: $brand-white;
			right: 18px;
			visibility: visible;
			opacity: 1;
		}
	}
}
// Form Input Field
.form-control {
	box-shadow: none;
	background: transparent;
	border: 2px solid rgba(0, 0, 0, 0.1);
	height: 54px;
	font-size: 18px;
	font-weight: 300;
  	&:active, &:focus {
  		outline: none;
		box-shadow: none;
		border-color: $brand-primary;
  }
}

.row-pb-md {
	padding-bottom: 4em!important;
}
.row-pb-sm {
	padding-bottom: 2em!important;
}

.fh5co-loader {
	position: fixed;
	left: 0px;
	top: 0px;
	width: 100%;
	height: 100%;
	z-index: 9999;
	background: url(../images/loader.gif) center no-repeat #fff;
}

.animate-box {
	.js & {
		opacity: 0;
	}
}