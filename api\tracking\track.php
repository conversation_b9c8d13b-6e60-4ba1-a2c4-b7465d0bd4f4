<?php
/**
 * Package Tracking API
 * Handles package tracking requests and returns shipment status
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Get tracking number from request
    $trackingNumber = null;
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }
        $trackingNumber = $input['tracking_number'] ?? null;
    } else {
        $trackingNumber = $_GET['tracking_number'] ?? null;
    }
    
    if (empty($trackingNumber)) {
        ApiResponse::error('Tracking number is required');
    }
    
    $trackingNumber = Database::sanitize($trackingNumber);
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get shipment information
    $shipmentQuery = "SELECT s.*, c.FirstName, c.LastName, c.Email, c.Phone
                      FROM Shipment s
                      LEFT JOIN Customer c ON s.CustomerID = c.CustomerID
                      WHERE s.TrackingNumber = ?";
    
    $stmt = $database->execute($shipmentQuery, [$trackingNumber]);
    $shipment = $stmt->fetch();
    
    if (!$shipment) {
        ApiResponse::error('Tracking number not found', 404);
    }
    
    // Get tracking events
    $eventsQuery = "SELECT EventType, EventDescription, Location, EventDateTime, Notes
                    FROM TrackingEvent
                    WHERE ShipmentID = ?
                    ORDER BY EventDateTime ASC";
    
    $eventsStmt = $database->execute($eventsQuery, [$shipment['ShipmentID']]);
    $events = $eventsStmt->fetchAll();
    
    // Get delivery information if delivered
    $delivery = null;
    if ($shipment['Status'] === 'Delivered') {
        $deliveryQuery = "SELECT DeliveryDate, RecipientName, SignatureType, DeliveryNotes, DeliveryStatus
                          FROM Delivery
                          WHERE ShipmentID = ?
                          ORDER BY DeliveryDate DESC
                          LIMIT 1";
        
        $deliveryStmt = $database->execute($deliveryQuery, [$shipment['ShipmentID']]);
        $delivery = $deliveryStmt->fetch();
    }
    
    // Format tracking events for response
    $formattedEvents = [];
    foreach ($events as $event) {
        $formattedEvents[] = [
            'type' => $event['EventType'],
            'description' => $event['EventDescription'],
            'location' => $event['Location'],
            'datetime' => $event['EventDateTime'],
            'notes' => $event['Notes'],
            'completed' => true // All recorded events are completed
        ];
    }
    
    // Add future events based on current status
    $currentStatus = $shipment['Status'];
    $futureEvents = [];
    
    switch ($currentStatus) {
        case 'Pending':
            $futureEvents = [
                ['type' => 'Picked Up', 'description' => 'Package will be collected', 'completed' => false],
                ['type' => 'In Transit', 'description' => 'Package in transit to destination', 'completed' => false],
                ['type' => 'Out for Delivery', 'description' => 'Package out for delivery', 'completed' => false],
                ['type' => 'Delivered', 'description' => 'Package delivered successfully', 'completed' => false]
            ];
            break;
        case 'Picked Up':
            $futureEvents = [
                ['type' => 'In Transit', 'description' => 'Package in transit to destination', 'completed' => false],
                ['type' => 'Out for Delivery', 'description' => 'Package out for delivery', 'completed' => false],
                ['type' => 'Delivered', 'description' => 'Package delivered successfully', 'completed' => false]
            ];
            break;
        case 'In Transit':
            $futureEvents = [
                ['type' => 'Out for Delivery', 'description' => 'Package out for delivery', 'completed' => false],
                ['type' => 'Delivered', 'description' => 'Package delivered successfully', 'completed' => false]
            ];
            break;
        case 'Out for Delivery':
            $futureEvents = [
                ['type' => 'Delivered', 'description' => 'Package delivered successfully', 'completed' => false]
            ];
            break;
    }
    
    // Combine current and future events
    $allEvents = array_merge($formattedEvents, $futureEvents);
    
    // Calculate delivery progress percentage
    $totalSteps = 5; // Pending, Picked Up, In Transit, Out for Delivery, Delivered
    $currentStep = 0;
    
    switch ($currentStatus) {
        case 'Pending': $currentStep = 1; break;
        case 'Picked Up': $currentStep = 2; break;
        case 'In Transit': $currentStep = 3; break;
        case 'Out for Delivery': $currentStep = 4; break;
        case 'Delivered': $currentStep = 5; break;
        case 'Cancelled': $currentStep = 0; break;
    }
    
    $progressPercentage = ($currentStep / $totalSteps) * 100;
    
    // Prepare response data
    $responseData = [
        'shipment' => [
            'tracking_number' => $shipment['TrackingNumber'],
            'status' => $shipment['Status'],
            'priority' => $shipment['Priority'],
            'shipment_type' => $shipment['ShipmentType'],
            'delivery_mode' => $shipment['DeliveryMode'],
            'weight' => $shipment['Weight'],
            'dimensions' => $shipment['Dimensions'],
            'declared_value' => $shipment['DeclaredValue'],
            'shipping_cost' => $shipment['ShippingCost'],
            'estimated_delivery_date' => $shipment['EstimatedDeliveryDate'],
            'special_instructions' => $shipment['SpecialInstructions'],
            'created_at' => $shipment['CreatedAt']
        ],
        'sender' => [
            'name' => $shipment['SenderName'],
            'address' => $shipment['SenderAddress'],
            'phone' => $shipment['SenderPhone']
        ],
        'recipient' => [
            'name' => $shipment['RecipientName'],
            'address' => $shipment['RecipientAddress'],
            'phone' => $shipment['RecipientPhone']
        ],
        'customer' => [
            'name' => $shipment['FirstName'] . ' ' . $shipment['LastName'],
            'email' => $shipment['Email'],
            'phone' => $shipment['Phone']
        ],
        'tracking' => [
            'events' => $allEvents,
            'progress_percentage' => $progressPercentage,
            'current_step' => $currentStep,
            'total_steps' => $totalSteps
        ],
        'delivery' => $delivery
    ];
    
    // Log tracking request for analytics
    $database->logAnalytics(
        null, // No user ID for public tracking
        'TRACK_PACKAGE',
        'Tracking',
        [
            'tracking_number' => $trackingNumber,
            'status' => $shipment['Status'],
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]
    );
    
    ApiResponse::success($responseData, 'Tracking information retrieved successfully');
    
} catch (Exception $e) {
    error_log("Tracking error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            null,
            'TRACKING_ERROR',
            $e->getMessage(),
            $e->getTraceAsString(),
            ['tracking_number' => $trackingNumber ?? 'unknown']
        );
    }
    
    ApiResponse::serverError('An error occurred while retrieving tracking information');
}
?>
