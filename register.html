<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Create Account &mdash; ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="Create your ZamSend account to access international courier services, track packages, and manage shipments." />
	<meta name="keywords" content="ZamSend registration, create account, courier signup, shipping account" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">

	<!-- Custom registration styles -->
	<style>
		.register-hero {
			position: relative;
			min-height: 100vh;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 140px 0 80px 0;
		}

		.register-hero video {
			position: fixed;
			top: 0;
			left: 0;
			min-width: 100%;
			min-height: 100%;
			width: auto;
			height: auto;
			z-index: -2;
			object-fit: cover;
		}

		.register-hero::before {
			content: '';
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg, rgba(102, 126, 234, 0.85) 0%, rgba(118, 75, 162, 0.85) 100%);
			z-index: -1;
		}

		.register-container {
			background: rgba(255, 255, 255, 0.98);
			backdrop-filter: blur(15px);
			border-radius: 20px;
			padding: 30px;
			box-shadow: 0 25px 50px rgba(0,0,0,0.15);
			max-width: 600px;
			width: 100%;
			margin: 0 20px;
			max-height: 90vh;
			overflow-y: auto;
		}
		
		.register-header {
			text-align: center;
			margin-bottom: 25px;
		}

		.register-header h1 {
			color: #333;
			margin-bottom: 8px;
			font-size: 26px;
			font-weight: 700;
		}

		.register-header p {
			color: #666;
			margin-bottom: 0;
			font-size: 15px;
		}
		
		.form-group {
			margin-bottom: 15px;
		}

		.form-control {
			height: 45px;
			border-radius: 8px;
			border: 2px solid #e1e5e9;
			padding: 0 15px;
			font-size: 14px;
			transition: all 0.3s ease;
			background: rgba(255, 255, 255, 0.9);
		}

		.form-control:focus {
			border-color: #667eea;
			box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
			background: white;
		}

		.form-control::placeholder {
			color: #999;
			font-size: 14px;
		}

		textarea.form-control {
			height: auto;
			min-height: 80px;
			padding: 12px 15px;
			resize: vertical;
		}
		
		.btn-register {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 25px;
			color: white;
			font-size: 16px;
			font-weight: 600;
			padding: 15px 40px;
			min-width: 200px;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
		}

		.btn-register:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
			color: white;
		}

		.btn-register:disabled {
			opacity: 0.6;
			transform: none;
			box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
		}
		
		.form-check {
			margin: 20px 0;
		}
		
		.form-check-input {
			margin-top: 0.3rem;
		}
		
		.form-check-label {
			font-size: 14px;
			color: #666;
		}
		
		.form-check-label a {
			color: #667eea;
			text-decoration: none;
		}
		
		.form-check-label a:hover {
			text-decoration: underline;
		}
		
		.login-link {
			text-align: center;
			margin-top: 25px;
			padding-top: 20px;
			border-top: 1px solid rgba(0,0,0,0.1);
		}

		.login-link a {
			color: #667eea;
			text-decoration: none;
			font-weight: 600;
			font-size: 15px;
		}

		.login-link a:hover {
			text-decoration: underline;
			color: #5a67d8;
		}

		/* Custom scrollbar for the container */
		.register-container::-webkit-scrollbar {
			width: 6px;
		}

		.register-container::-webkit-scrollbar-track {
			background: rgba(0,0,0,0.1);
			border-radius: 3px;
		}

		.register-container::-webkit-scrollbar-thumb {
			background: rgba(102, 126, 234, 0.5);
			border-radius: 3px;
		}

		.register-container::-webkit-scrollbar-thumb:hover {
			background: rgba(102, 126, 234, 0.7);
		}

		/* Responsive adjustments */
		@media (max-width: 768px) {
			.register-hero {
				padding: 120px 0 60px 0;
			}

			.register-container {
				margin: 0 15px;
				padding: 25px;
				max-height: 85vh;
			}

			.register-header h1 {
				font-size: 24px;
			}

			.form-section {
				padding: 15px;
			}

			.account-type-selector {
				flex-direction: column;
				gap: 10px;
			}

			.btn-register {
				width: 100%;
				min-width: auto;
			}
		}
		
		.alert {
			border-radius: 10px;
			margin-bottom: 20px;
		}
		
		.loading {
			display: none;
		}
		
		.loading .spinner-border {
			width: 20px;
			height: 20px;
		}
		
		.password-strength {
			margin-top: 8px;
			font-size: 12px;
			padding: 5px 10px;
			border-radius: 4px;
			background: rgba(0,0,0,0.05);
		}

		.strength-weak {
			color: #dc3545;
			background: rgba(220, 53, 69, 0.1);
		}
		.strength-medium {
			color: #ffc107;
			background: rgba(255, 193, 7, 0.1);
		}
		.strength-strong {
			color: #28a745;
			background: rgba(40, 167, 69, 0.1);
		}

		.form-control.is-valid {
			border-color: #28a745;
		}

		.form-control.is-invalid {
			border-color: #dc3545;
		}

		.invalid-feedback {
			display: block;
			color: #dc3545;
			font-size: 12px;
			margin-top: 5px;
		}
		
		.input-group-text {
			background: transparent;
			border-left: none;
			cursor: pointer;
		}
		
		.account-type-selector {
			display: flex;
			gap: 12px;
			margin-bottom: 20px;
		}

		.account-type {
			flex: 1;
			padding: 12px;
			border: 2px solid #e9ecef;
			border-radius: 8px;
			text-align: center;
			cursor: pointer;
			transition: all 0.3s ease;
			background: rgba(255, 255, 255, 0.8);
		}

		.account-type:hover,
		.account-type.selected {
			border-color: #667eea;
			background: rgba(102, 126, 234, 0.1);
			transform: translateY(-1px);
		}

		.account-type h5 {
			margin-bottom: 3px;
			color: #333;
			font-size: 16px;
			font-weight: 600;
		}

		.account-type small {
			color: #666;
			font-size: 12px;
		}

		.form-section {
			background: rgba(248, 249, 250, 0.8);
			border-radius: 10px;
			padding: 20px;
			margin-bottom: 20px;
			border: 1px solid rgba(0,0,0,0.1);
		}

		.form-section h4 {
			color: #667eea;
			margin-bottom: 15px;
			font-size: 18px;
			font-weight: 600;
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.form-section h4 i {
			font-size: 20px;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<nav class="fh5co-nav" role="navigation" style="position: absolute; top: 0; width: 100%; z-index: 1000; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num" style="color: white;">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook" style="color: white;"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter" style="color: white;"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn" style="color: white;"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp" style="color: white;"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html" style="color: white;">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html" style="color: white;">Home</a></li>
							<li><a href="services.html" style="color: white;">Services</a></li>
							<li><a href="tracking.html" style="color: white;">Track Package</a></li>
							<li class="has-dropdown">
								<a href="pricing.html" style="color: white;">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html" style="color: white;">About</a></li>
							<li><a href="contact.html" style="color: white;">Contact</a></li>
							<li class="btn-cta"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta active"><a href="register.html"><span>Sign Up</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="register-hero">
		<video autoplay muted loop>
			<source src="videos/back.mp4" type="video/mp4">
			Your browser does not support the video tag.
		</video>
		
		<div class="register-container">
			<div class="register-header">
				<h1>Create Your Account</h1>
				<p>Join ZamSend and start shipping worldwide</p>
			</div>
			
			<div id="alert-container"></div>
			
			<form id="registrationForm">
				<!-- Account Type Selection -->
				<div class="form-section">
					<h4><i class="icon-user"></i> Account Type</h4>
					<div class="account-type-selector">
						<div class="account-type selected" data-type="personal">
							<h5>Personal</h5>
							<small>Individual shipping</small>
						</div>
						<div class="account-type" data-type="business">
							<h5>Business</h5>
							<small>Company account</small>
						</div>
					</div>
					<input type="hidden" id="accountType" name="account_type" value="personal">
				</div>

				<!-- Personal Information -->
				<div class="form-section">
					<h4><i class="icon-user"></i> Personal Information</h4>
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="first_name" placeholder="First Name" required>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="last_name" placeholder="Last Name" required>
							</div>
						</div>
					</div>

					<div class="form-group">
						<input type="email" class="form-control" name="email" placeholder="Email Address" required>
					</div>

					<div class="form-group">
						<input type="tel" class="form-control" name="phone" placeholder="Phone Number (+260-xxx-xxxxxx)" required>
					</div>
				</div>
				
				<!-- Security Information -->
				<div class="form-section">
					<h4><i class="icon-lock"></i> Security</h4>
					<div class="form-group">
						<div class="input-group">
							<input type="password" class="form-control" name="password" id="password" placeholder="Create Password (min. 8 characters)" required>
							<div class="input-group-append">
								<span class="input-group-text" onclick="togglePassword('password')" style="cursor: pointer;">
									<i class="icon-eye" id="password-eye"></i>
								</span>
							</div>
						</div>
						<div id="password-strength" class="password-strength"></div>
					</div>

					<div class="form-group">
						<div class="input-group">
							<input type="password" class="form-control" name="confirm_password" id="confirm_password" placeholder="Confirm Password" required>
							<div class="input-group-append">
								<span class="input-group-text" onclick="togglePassword('confirm_password')" style="cursor: pointer;">
									<i class="icon-eye" id="confirm_password-eye"></i>
								</span>
							</div>
						</div>
					</div>
				</div>
				
				<!-- Address Information -->
				<div class="form-section">
					<h4><i class="icon-location"></i> Address Information</h4>
					<div class="form-group">
						<textarea class="form-control" name="address" rows="2" placeholder="Complete Street Address" required></textarea>
					</div>

					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="city" placeholder="City" required>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="postal_code" placeholder="Postal Code" required>
							</div>
						</div>
					</div>
				</div>
				
				<!-- Business Information (Hidden by default) -->
				<div id="business-fields" class="form-section" style="display: none;">
					<h4><i class="icon-briefcase"></i> Business Information</h4>
					<div class="form-group">
						<input type="text" class="form-control" name="company_name" placeholder="Company Name">
					</div>

					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="registration_number" placeholder="Registration Number">
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="tax_id" placeholder="Tax ID / VAT Number">
							</div>
						</div>
					</div>

					<div class="form-group">
						<select class="form-control" name="industry">
							<option value="">Select Industry</option>
							<option value="Retail">Retail & E-commerce</option>
							<option value="Manufacturing">Manufacturing</option>
							<option value="Technology">Technology & IT</option>
							<option value="Healthcare">Healthcare & Medical</option>
							<option value="Education">Education & Training</option>
							<option value="Finance">Finance & Banking</option>
							<option value="Agriculture">Agriculture & Mining</option>
							<option value="Tourism">Tourism & Hospitality</option>
							<option value="Other">Other</option>
						</select>
					</div>
				</div>
				
				<!-- Terms and Preferences -->
				<div class="form-section">
					<h4><i class="icon-check"></i> Terms & Preferences</h4>
					<div class="form-check" style="margin-bottom: 15px;">
						<input type="checkbox" class="form-check-input" id="terms" name="terms" required>
						<label class="form-check-label" for="terms">
							I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a>
						</label>
					</div>

					<div class="form-check">
						<input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
						<label class="form-check-label" for="newsletter">
							Subscribe to our newsletter for shipping updates and promotions
						</label>
					</div>
				</div>

				<!-- Submit Button -->
				<div style="text-align: center; margin-top: 20px;">
					<button type="submit" class="btn btn-register">
						<span class="register-text"><i class="icon-user-plus"></i> Create Account</span>
						<span class="loading">
							<span class="spinner-border spinner-border-sm" role="status"></span>
							Creating account...
						</span>
					</button>
				</div>
			</form>
			
			<div class="login-link">
				Already have an account? <a href="login.html">Sign in here</a>
			</div>
		</div>
	</div>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	
	<script>
		$(document).ready(function() {
			// Account type selection
			$('.account-type').on('click', function() {
				$('.account-type').removeClass('selected');
				$(this).addClass('selected');

				const type = $(this).data('type');
				$('#accountType').val(type);

				if (type === 'business') {
					$('#business-fields').slideDown(300);
					$('#business-fields input, #business-fields select').prop('required', true);
				} else {
					$('#business-fields').slideUp(300);
					$('#business-fields input, #business-fields select').prop('required', false);
				}
			});

			// Password strength checker
			$('#password').on('input', function() {
				checkPasswordStrength($(this).val());
				validatePasswordMatch();
			});

			// Confirm password validation
			$('#confirm_password').on('input', function() {
				validatePasswordMatch();
			});

			// Email validation
			$('input[name="email"]').on('blur', function() {
				validateEmail($(this));
			});

			// Phone validation
			$('input[name="phone"]').on('blur', function() {
				validatePhone($(this));
			});

			// Form submission
			$('#registrationForm').on('submit', function(e) {
				e.preventDefault();

				if (validateForm()) {
					registerUser();
				}
			});
		});
		
		function togglePassword(fieldId) {
			const field = document.getElementById(fieldId);
			const eye = document.getElementById(fieldId + '-eye');
			
			if (field.type === 'password') {
				field.type = 'text';
				eye.className = 'icon-eye-off';
			} else {
				field.type = 'password';
				eye.className = 'icon-eye';
			}
		}
		
		function checkPasswordStrength(password) {
			const strengthDiv = $('#password-strength');
			const passwordField = $('#password');
			let strength = 0;
			let message = '';

			if (password.length >= 8) strength++;
			if (/[a-z]/.test(password)) strength++;
			if (/[A-Z]/.test(password)) strength++;
			if (/[0-9]/.test(password)) strength++;
			if (/[^A-Za-z0-9]/.test(password)) strength++;

			switch (strength) {
				case 0:
				case 1:
				case 2:
					message = '<span class="strength-weak">Weak - Add uppercase, numbers, and symbols</span>';
					passwordField.removeClass('is-valid').addClass('is-invalid');
					break;
				case 3:
				case 4:
					message = '<span class="strength-medium">Medium - Add more complexity</span>';
					passwordField.removeClass('is-invalid is-valid');
					break;
				case 5:
					message = '<span class="strength-strong">Strong password ✓</span>';
					passwordField.removeClass('is-invalid').addClass('is-valid');
					break;
			}

			strengthDiv.html(message);
		}

		function validatePasswordMatch() {
			const password = $('#password').val();
			const confirmPassword = $('#confirm_password').val();
			const confirmField = $('#confirm_password');

			if (confirmPassword.length > 0) {
				if (password === confirmPassword) {
					confirmField.removeClass('is-invalid').addClass('is-valid');
				} else {
					confirmField.removeClass('is-valid').addClass('is-invalid');
				}
			}
		}

		function validateEmail(field) {
			const email = field.val();
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

			if (email.length > 0) {
				if (emailRegex.test(email)) {
					field.removeClass('is-invalid').addClass('is-valid');
				} else {
					field.removeClass('is-valid').addClass('is-invalid');
				}
			}
		}

		function validatePhone(field) {
			const phone = field.val();
			const phoneRegex = /^\+?[0-9]{7,15}$/;
			const cleanPhone = phone.replace(/[^0-9+]/g, '');

			if (phone.length > 0) {
				if (phoneRegex.test(cleanPhone)) {
					field.removeClass('is-invalid').addClass('is-valid');
				} else {
					field.removeClass('is-valid').addClass('is-invalid');
				}
			}
		}
		
		function validateForm() {
			let isValid = true;
			let errorMessages = [];

			// Clear previous validation states
			$('.form-control').removeClass('is-invalid');

			// Validate required fields
			$('input[required], select[required], textarea[required]').each(function() {
				if (!$(this).val().trim()) {
					$(this).addClass('is-invalid');
					isValid = false;
				}
			});

			// Validate email
			const email = $('input[name="email"]').val();
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(email)) {
				$('input[name="email"]').addClass('is-invalid');
				errorMessages.push('Please enter a valid email address');
				isValid = false;
			}

			// Validate phone
			const phone = $('input[name="phone"]').val();
			const phoneRegex = /^\+?[0-9]{7,15}$/;
			const cleanPhone = phone.replace(/[^0-9+]/g, '');
			if (!phoneRegex.test(cleanPhone)) {
				$('input[name="phone"]').addClass('is-invalid');
				errorMessages.push('Please enter a valid phone number');
				isValid = false;
			}

			// Validate password
			const password = $('input[name="password"]').val();
			const confirmPassword = $('input[name="confirm_password"]').val();

			if (password.length < 8) {
				$('input[name="password"]').addClass('is-invalid');
				errorMessages.push('Password must be at least 8 characters long');
				isValid = false;
			}

			if (password !== confirmPassword) {
				$('input[name="confirm_password"]').addClass('is-invalid');
				errorMessages.push('Passwords do not match');
				isValid = false;
			}

			// Validate terms acceptance
			if (!$('#terms').is(':checked')) {
				errorMessages.push('Please accept the Terms of Service');
				isValid = false;
			}

			// Show errors if any
			if (!isValid) {
				showAlert(errorMessages.join('<br>'), 'danger');
			}

			return isValid;
		}
		
		function registerUser() {
			const formData = new FormData($('#registrationForm')[0]);
			const data = {};
			
			// Convert FormData to object
			for (let [key, value] of formData.entries()) {
				if (key === 'terms' || key === 'newsletter') {
					data[key] = value === 'on';
				} else {
					data[key] = value;
				}
			}
			
			// Show loading state
			$('.register-text').hide();
			$('.loading').show();
			$('#registrationForm button').prop('disabled', true);
			
			$.ajax({
				url: 'api/auth/register.php',
				method: 'POST',
				data: JSON.stringify(data),
				contentType: 'application/json',
				success: function(response) {
					if (response.success) {
						showAlert('Account created successfully! Please check your email for verification.', 'success');
						setTimeout(function() {
							window.location.href = 'login.html?registered=1';
						}, 3000);
					}
				},
				error: function(xhr) {
					const response = JSON.parse(xhr.responseText);
					showAlert(response.message || 'Registration failed. Please try again.', 'danger');
				},
				complete: function() {
					// Hide loading state
					$('.register-text').show();
					$('.loading').hide();
					$('#registrationForm button').prop('disabled', false);
				}
			});
		}
		
		function showAlert(message, type) {
			const alertHtml = `
				<div class="alert alert-${type} alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert">
						<span>&times;</span>
					</button>
					${message}
				</div>
			`;
			$('#alert-container').html(alertHtml);
		}
	</script>

	</body>
</html>
