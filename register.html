<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Create Account &mdash; ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="Create your ZamSend account to access international courier services, track packages, and manage shipments." />
	<meta name="keywords" content="ZamSend registration, create account, courier signup, shipping account" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">

	<!-- Custom registration styles -->
	<style>
		.register-hero {
			position: relative;
			height: 100vh;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.register-hero video {
			position: absolute;
			top: 50%;
			left: 50%;
			min-width: 100%;
			min-height: 100%;
			width: auto;
			height: auto;
			transform: translateX(-50%) translateY(-50%);
			z-index: -2;
		}
		
		.register-hero::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
			z-index: -1;
		}
		
		.register-container {
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(10px);
			border-radius: 20px;
			padding: 40px;
			box-shadow: 0 20px 40px rgba(0,0,0,0.1);
			max-width: 500px;
			width: 100%;
			margin: 0 20px;
		}
		
		.register-header {
			text-align: center;
			margin-bottom: 30px;
		}
		
		.register-header h1 {
			color: #333;
			margin-bottom: 10px;
			font-size: 28px;
		}
		
		.register-header p {
			color: #666;
			margin-bottom: 0;
		}
		
		.form-group {
			margin-bottom: 20px;
		}
		
		.form-control {
			height: 50px;
			border-radius: 10px;
			border: 2px solid #e1e5e9;
			padding: 0 20px;
			font-size: 16px;
			transition: all 0.3s ease;
		}
		
		.form-control:focus {
			border-color: #667eea;
			box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
		}
		
		.btn-register {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 25px;
			color: white;
			font-size: 18px;
			font-weight: 600;
			padding: 15px 30px;
			width: 100%;
			transition: all 0.3s ease;
		}
		
		.btn-register:hover {
			transform: translateY(-2px);
			box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
			color: white;
		}
		
		.btn-register:disabled {
			opacity: 0.6;
			transform: none;
			box-shadow: none;
		}
		
		.form-check {
			margin: 20px 0;
		}
		
		.form-check-input {
			margin-top: 0.3rem;
		}
		
		.form-check-label {
			font-size: 14px;
			color: #666;
		}
		
		.form-check-label a {
			color: #667eea;
			text-decoration: none;
		}
		
		.form-check-label a:hover {
			text-decoration: underline;
		}
		
		.login-link {
			text-align: center;
			margin-top: 20px;
			padding-top: 20px;
			border-top: 1px solid #e9ecef;
		}
		
		.login-link a {
			color: #667eea;
			text-decoration: none;
			font-weight: 600;
		}
		
		.login-link a:hover {
			text-decoration: underline;
		}
		
		.alert {
			border-radius: 10px;
			margin-bottom: 20px;
		}
		
		.loading {
			display: none;
		}
		
		.loading .spinner-border {
			width: 20px;
			height: 20px;
		}
		
		.password-strength {
			margin-top: 5px;
			font-size: 12px;
		}
		
		.strength-weak { color: #dc3545; }
		.strength-medium { color: #ffc107; }
		.strength-strong { color: #28a745; }
		
		.input-group-text {
			background: transparent;
			border-left: none;
			cursor: pointer;
		}
		
		.account-type-selector {
			display: flex;
			gap: 15px;
			margin-bottom: 20px;
		}
		
		.account-type {
			flex: 1;
			padding: 15px;
			border: 2px solid #e9ecef;
			border-radius: 10px;
			text-align: center;
			cursor: pointer;
			transition: all 0.3s ease;
		}
		
		.account-type:hover,
		.account-type.selected {
			border-color: #667eea;
			background: rgba(102, 126, 234, 0.1);
		}
		
		.account-type h5 {
			margin-bottom: 5px;
			color: #333;
		}
		
		.account-type small {
			color: #666;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<nav class="fh5co-nav" role="navigation" style="position: absolute; top: 0; width: 100%; z-index: 1000; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num" style="color: white;">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook" style="color: white;"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter" style="color: white;"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn" style="color: white;"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp" style="color: white;"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html" style="color: white;">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html" style="color: white;">Home</a></li>
							<li><a href="services.html" style="color: white;">Services</a></li>
							<li><a href="tracking.html" style="color: white;">Track Package</a></li>
							<li class="has-dropdown">
								<a href="pricing.html" style="color: white;">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html" style="color: white;">About</a></li>
							<li><a href="contact.html" style="color: white;">Contact</a></li>
							<li class="btn-cta"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta active"><a href="register.html"><span>Sign Up</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="register-hero">
		<video autoplay muted loop>
			<source src="videos/back.mp4" type="video/mp4">
			Your browser does not support the video tag.
		</video>
		
		<div class="register-container">
			<div class="register-header">
				<h1>Create Your Account</h1>
				<p>Join ZamSend and start shipping worldwide</p>
			</div>
			
			<div id="alert-container"></div>
			
			<form id="registrationForm">
				<!-- Account Type Selection -->
				<div class="account-type-selector">
					<div class="account-type selected" data-type="personal">
						<h5>Personal</h5>
						<small>Individual shipping</small>
					</div>
					<div class="account-type" data-type="business">
						<h5>Business</h5>
						<small>Company account</small>
					</div>
				</div>
				<input type="hidden" id="accountType" name="account_type" value="personal">
				
				<!-- Personal Information -->
				<div class="row">
					<div class="col-md-6">
						<div class="form-group">
							<input type="text" class="form-control" name="first_name" placeholder="First Name" required>
						</div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
							<input type="text" class="form-control" name="last_name" placeholder="Last Name" required>
						</div>
					</div>
				</div>
				
				<div class="form-group">
					<input type="email" class="form-control" name="email" placeholder="Email Address" required>
				</div>
				
				<div class="form-group">
					<input type="tel" class="form-control" name="phone" placeholder="Phone Number" required>
				</div>
				
				<!-- Password Fields -->
				<div class="form-group">
					<div class="input-group">
						<input type="password" class="form-control" name="password" id="password" placeholder="Password" required>
						<div class="input-group-append">
							<span class="input-group-text" onclick="togglePassword('password')">
								<i class="icon-eye" id="password-eye"></i>
							</span>
						</div>
					</div>
					<div id="password-strength" class="password-strength"></div>
				</div>
				
				<div class="form-group">
					<div class="input-group">
						<input type="password" class="form-control" name="confirm_password" id="confirm_password" placeholder="Confirm Password" required>
						<div class="input-group-append">
							<span class="input-group-text" onclick="togglePassword('confirm_password')">
								<i class="icon-eye" id="confirm_password-eye"></i>
							</span>
						</div>
					</div>
				</div>
				
				<!-- Address Information -->
				<div class="form-group">
					<textarea class="form-control" name="address" rows="2" placeholder="Complete Address" required></textarea>
				</div>
				
				<div class="row">
					<div class="col-md-6">
						<div class="form-group">
							<input type="text" class="form-control" name="city" placeholder="City" required>
						</div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
							<input type="text" class="form-control" name="postal_code" placeholder="Postal Code" required>
						</div>
					</div>
				</div>
				
				<!-- Business Information (Hidden by default) -->
				<div id="business-fields" style="display: none;">
					<div class="form-group">
						<input type="text" class="form-control" name="company_name" placeholder="Company Name">
					</div>
					
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="registration_number" placeholder="Registration Number">
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<input type="text" class="form-control" name="tax_id" placeholder="Tax ID">
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<select class="form-control" name="industry">
							<option value="">Select Industry</option>
							<option value="Retail">Retail</option>
							<option value="Manufacturing">Manufacturing</option>
							<option value="Technology">Technology</option>
							<option value="Healthcare">Healthcare</option>
							<option value="Education">Education</option>
							<option value="Finance">Finance</option>
							<option value="Other">Other</option>
						</select>
					</div>
				</div>
				
				<!-- Terms and Conditions -->
				<div class="form-check">
					<input type="checkbox" class="form-check-input" id="terms" name="terms" required>
					<label class="form-check-label" for="terms">
						I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a>
					</label>
				</div>
				
				<div class="form-check">
					<input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
					<label class="form-check-label" for="newsletter">
						Subscribe to our newsletter for shipping updates and promotions
					</label>
				</div>
				
				<!-- Submit Button -->
				<button type="submit" class="btn btn-register">
					<span class="register-text">Create Account</span>
					<span class="loading">
						<span class="spinner-border spinner-border-sm" role="status"></span>
						Creating account...
					</span>
				</button>
			</form>
			
			<div class="login-link">
				Already have an account? <a href="login.html">Sign in here</a>
			</div>
		</div>
	</div>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	
	<script>
		$(document).ready(function() {
			// Account type selection
			$('.account-type').on('click', function() {
				$('.account-type').removeClass('selected');
				$(this).addClass('selected');
				
				const type = $(this).data('type');
				$('#accountType').val(type);
				
				if (type === 'business') {
					$('#business-fields').show();
					$('#business-fields input, #business-fields select').prop('required', true);
				} else {
					$('#business-fields').hide();
					$('#business-fields input, #business-fields select').prop('required', false);
				}
			});
			
			// Password strength checker
			$('#password').on('input', function() {
				checkPasswordStrength($(this).val());
			});
			
			// Form submission
			$('#registrationForm').on('submit', function(e) {
				e.preventDefault();
				
				if (validateForm()) {
					registerUser();
				}
			});
		});
		
		function togglePassword(fieldId) {
			const field = document.getElementById(fieldId);
			const eye = document.getElementById(fieldId + '-eye');
			
			if (field.type === 'password') {
				field.type = 'text';
				eye.className = 'icon-eye-off';
			} else {
				field.type = 'password';
				eye.className = 'icon-eye';
			}
		}
		
		function checkPasswordStrength(password) {
			const strengthDiv = $('#password-strength');
			let strength = 0;
			let message = '';
			
			if (password.length >= 8) strength++;
			if (/[a-z]/.test(password)) strength++;
			if (/[A-Z]/.test(password)) strength++;
			if (/[0-9]/.test(password)) strength++;
			if (/[^A-Za-z0-9]/.test(password)) strength++;
			
			switch (strength) {
				case 0:
				case 1:
				case 2:
					message = '<span class="strength-weak">Weak password</span>';
					break;
				case 3:
				case 4:
					message = '<span class="strength-medium">Medium password</span>';
					break;
				case 5:
					message = '<span class="strength-strong">Strong password</span>';
					break;
			}
			
			strengthDiv.html(message);
		}
		
		function validateForm() {
			const password = $('input[name="password"]').val();
			const confirmPassword = $('input[name="confirm_password"]').val();
			
			if (password !== confirmPassword) {
				showAlert('Passwords do not match', 'danger');
				return false;
			}
			
			if (password.length < 8) {
				showAlert('Password must be at least 8 characters long', 'danger');
				return false;
			}
			
			if (!$('#terms').is(':checked')) {
				showAlert('Please accept the Terms of Service', 'danger');
				return false;
			}
			
			return true;
		}
		
		function registerUser() {
			const formData = new FormData($('#registrationForm')[0]);
			const data = {};
			
			// Convert FormData to object
			for (let [key, value] of formData.entries()) {
				if (key === 'terms' || key === 'newsletter') {
					data[key] = value === 'on';
				} else {
					data[key] = value;
				}
			}
			
			// Show loading state
			$('.register-text').hide();
			$('.loading').show();
			$('#registrationForm button').prop('disabled', true);
			
			$.ajax({
				url: 'api/auth/register.php',
				method: 'POST',
				data: JSON.stringify(data),
				contentType: 'application/json',
				success: function(response) {
					if (response.success) {
						showAlert('Account created successfully! Please check your email for verification.', 'success');
						setTimeout(function() {
							window.location.href = 'login.html?registered=1';
						}, 3000);
					}
				},
				error: function(xhr) {
					const response = JSON.parse(xhr.responseText);
					showAlert(response.message || 'Registration failed. Please try again.', 'danger');
				},
				complete: function() {
					// Hide loading state
					$('.register-text').show();
					$('.loading').hide();
					$('#registrationForm button').prop('disabled', false);
				}
			});
		}
		
		function showAlert(message, type) {
			const alertHtml = `
				<div class="alert alert-${type} alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert">
						<span>&times;</span>
					</button>
					${message}
				</div>
			`;
			$('#alert-container').html(alertHtml);
		}
	</script>

	</body>
</html>
