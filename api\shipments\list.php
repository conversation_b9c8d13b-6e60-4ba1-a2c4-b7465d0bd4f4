<?php
/**
 * List Shipments API
 * Handles retrieval of shipment list with pagination, search, and filtering
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Require authentication
    SessionManager::requireLogin();
    $userID = SessionManager::getUserId();
    $userRole = SessionManager::getUserRole();
    
    // Get query parameters
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(10, (int)$_GET['limit'])) : 20;
    $search = isset($_GET['search']) ? Database::sanitize($_GET['search']) : '';
    $status = isset($_GET['status']) ? Database::sanitize($_GET['status']) : 'all';
    $customerID = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : null;
    $dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : null;
    $dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : null;
    $sortBy = isset($_GET['sort_by']) ? Database::sanitize($_GET['sort_by']) : 'created_at';
    $sortOrder = isset($_GET['sort_order']) && strtolower($_GET['sort_order']) === 'asc' ? 'ASC' : 'DESC';
    
    $offset = ($page - 1) * $limit;
    
    // Validate sort field
    $allowedSortFields = ['tracking_number', 'recipient_name', 'status', 'created_at', 'estimated_delivery_date', 'shipping_cost'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'created_at';
    }
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Build WHERE clause
    $whereConditions = [];
    $params = [];
    
    // Role-based filtering
    if ($userRole === 'CustomerService' || $userRole === 'Logistics') {
        // These roles can see all shipments but with limited actions
    } elseif ($userRole === 'Customer') {
        // Customers can only see their own shipments
        $whereConditions[] = "s.CustomerID = ?";
        $params[] = $userID; // Assuming customer user ID matches customer ID
    }
    
    if ($status !== 'all') {
        $validStatuses = ['Pending', 'Picked Up', 'In Transit', 'Out for Delivery', 'Delivered', 'Cancelled'];
        if (in_array($status, $validStatuses)) {
            $whereConditions[] = "s.Status = ?";
            $params[] = $status;
        }
    }
    
    if ($customerID) {
        $whereConditions[] = "s.CustomerID = ?";
        $params[] = $customerID;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(s.TrackingNumber LIKE ? OR s.SenderName LIKE ? OR s.RecipientName LIKE ? OR c.FirstName LIKE ? OR c.LastName LIKE ?)";
        $searchParam = "%{$search}%";
        $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam, $searchParam]);
    }
    
    if ($dateFrom) {
        $whereConditions[] = "DATE(s.CreatedAt) >= ?";
        $params[] = $dateFrom;
    }
    
    if ($dateTo) {
        $whereConditions[] = "DATE(s.CreatedAt) <= ?";
        $params[] = $dateTo;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Get total count
    $countQuery = "SELECT COUNT(*) as total 
                   FROM Shipment s 
                   LEFT JOIN Customer c ON s.CustomerID = c.CustomerID 
                   $whereClause";
    $countStmt = $database->execute($countQuery, $params);
    $totalCount = $countStmt->fetch()['total'];
    
    // Get shipments with customer information
    $shipmentsQuery = "SELECT 
        s.ShipmentID, s.TrackingNumber, s.CustomerID, s.SenderName, s.SenderAddress, s.SenderPhone,
        s.RecipientName, s.RecipientAddress, s.RecipientPhone, s.ShipmentType, s.DeliveryMode,
        s.Weight, s.Dimensions, s.DeclaredValue, s.ShippingCost, s.Status, s.Priority,
        s.SpecialInstructions, s.EstimatedDeliveryDate, s.CreatedAt, s.UpdatedAt,
        c.FirstName, c.LastName, c.Email, c.Phone as CustomerPhone,
        (SELECT COUNT(*) FROM TrackingEvent te WHERE te.ShipmentID = s.ShipmentID) as EventCount,
        (SELECT EventDateTime FROM TrackingEvent te WHERE te.ShipmentID = s.ShipmentID ORDER BY EventDateTime DESC LIMIT 1) as LastEventDate
    FROM Shipment s
    LEFT JOIN Customer c ON s.CustomerID = c.CustomerID
    $whereClause
    ORDER BY s.$sortBy $sortOrder
    LIMIT ? OFFSET ?";
    
    $queryParams = array_merge($params, [$limit, $offset]);
    $shipmentsStmt = $database->execute($shipmentsQuery, $queryParams);
    $shipments = $shipmentsStmt->fetchAll();
    
    // Format shipment data
    $formattedShipments = [];
    foreach ($shipments as $shipment) {
        $formattedShipments[] = [
            'id' => (int)$shipment['ShipmentID'],
            'tracking_number' => $shipment['TrackingNumber'],
            'customer' => [
                'id' => (int)$shipment['CustomerID'],
                'name' => $shipment['FirstName'] . ' ' . $shipment['LastName'],
                'email' => $shipment['Email'],
                'phone' => $shipment['CustomerPhone']
            ],
            'sender' => [
                'name' => $shipment['SenderName'],
                'address' => $shipment['SenderAddress'],
                'phone' => $shipment['SenderPhone']
            ],
            'recipient' => [
                'name' => $shipment['RecipientName'],
                'address' => $shipment['RecipientAddress'],
                'phone' => $shipment['RecipientPhone']
            ],
            'shipment_details' => [
                'type' => $shipment['ShipmentType'],
                'delivery_mode' => $shipment['DeliveryMode'],
                'weight' => (float)$shipment['Weight'],
                'dimensions' => $shipment['Dimensions'],
                'declared_value' => $shipment['DeclaredValue'] ? (float)$shipment['DeclaredValue'] : null,
                'shipping_cost' => (float)$shipment['ShippingCost'],
                'priority' => $shipment['Priority'],
                'special_instructions' => $shipment['SpecialInstructions']
            ],
            'status' => $shipment['Status'],
            'estimated_delivery_date' => $shipment['EstimatedDeliveryDate'],
            'tracking_info' => [
                'event_count' => (int)$shipment['EventCount'],
                'last_event_date' => $shipment['LastEventDate']
            ],
            'created_at' => $shipment['CreatedAt'],
            'updated_at' => $shipment['UpdatedAt']
        ];
    }
    
    // Get status summary
    $statusSummaryQuery = "SELECT Status, COUNT(*) as count 
                          FROM Shipment s 
                          LEFT JOIN Customer c ON s.CustomerID = c.CustomerID 
                          $whereClause 
                          GROUP BY Status";
    $statusStmt = $database->execute($statusSummaryQuery, $params);
    $statusSummary = [];
    while ($row = $statusStmt->fetch()) {
        $statusSummary[$row['Status']] = (int)$row['count'];
    }
    
    // Calculate pagination info
    $totalPages = ceil($totalCount / $limit);
    $hasNextPage = $page < $totalPages;
    $hasPrevPage = $page > 1;
    
    // Log analytics
    $database->logAnalytics($userID, 'VIEW_SHIPMENTS', 'Shipments', [
        'page' => $page,
        'limit' => $limit,
        'search' => $search,
        'status_filter' => $status,
        'total_results' => $totalCount
    ]);
    
    // Prepare response data
    $responseData = [
        'shipments' => $formattedShipments,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_count' => $totalCount,
            'per_page' => $limit,
            'has_next_page' => $hasNextPage,
            'has_prev_page' => $hasPrevPage
        ],
        'filters' => [
            'search' => $search,
            'status' => $status,
            'customer_id' => $customerID,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'sort_by' => $sortBy,
            'sort_order' => $sortOrder
        ],
        'summary' => [
            'status_counts' => $statusSummary,
            'total_shipments' => $totalCount
        ]
    ];
    
    ApiResponse::success($responseData, 'Shipments retrieved successfully');
    
} catch (Exception $e) {
    error_log("List shipments error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            SessionManager::getUserId(),
            'LIST_SHIPMENTS_ERROR',
            $e->getMessage(),
            $e->getTraceAsString()
        );
    }
    
    ApiResponse::serverError('An error occurred while retrieving shipments');
}
?>
