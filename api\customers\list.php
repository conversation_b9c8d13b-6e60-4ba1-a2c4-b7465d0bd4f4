<?php
/**
 * List Customers API
 * Handles retrieval of customer list with pagination and search
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Require authentication and appropriate permissions
    SessionManager::requireLogin();
    $userID = SessionManager::getUserId();
    $userRole = SessionManager::getUserRole();
    
    // Check permissions
    $allowedRoles = ['SuperAdmin', 'SubAdmin', 'CustomerService'];
    if (!in_array($userRole, $allowedRoles)) {
        ApiResponse::forbidden('Insufficient permissions to view customers');
    }
    
    // Get query parameters
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(10, (int)$_GET['limit'])) : 20;
    $search = isset($_GET['search']) ? Database::sanitize($_GET['search']) : '';
    $status = isset($_GET['status']) ? Database::sanitize($_GET['status']) : 'all';
    $sortBy = isset($_GET['sort_by']) ? Database::sanitize($_GET['sort_by']) : 'created_at';
    $sortOrder = isset($_GET['sort_order']) && strtolower($_GET['sort_order']) === 'asc' ? 'ASC' : 'DESC';
    
    $offset = ($page - 1) * $limit;
    
    // Validate sort field
    $allowedSortFields = ['first_name', 'last_name', 'email', 'phone', 'city', 'created_at'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'created_at';
    }
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Build WHERE clause
    $whereConditions = [];
    $params = [];
    
    if ($status === 'active') {
        $whereConditions[] = "c.IsActive = 1";
    } elseif ($status === 'inactive') {
        $whereConditions[] = "c.IsActive = 0";
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(c.FirstName LIKE ? OR c.LastName LIKE ? OR c.Email LIKE ? OR c.Phone LIKE ?)";
        $searchParam = "%{$search}%";
        $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Get total count
    $countQuery = "SELECT COUNT(*) as total FROM Customer c $whereClause";
    $countStmt = $database->execute($countQuery, $params);
    $totalCount = $countStmt->fetch()['total'];
    
    // Get customers with company information
    $customersQuery = "SELECT 
        c.CustomerID, c.FirstName, c.LastName, c.Email, c.Phone, 
        c.Address, c.City, c.State, c.PostalCode, c.Country, 
        c.DateOfBirth, c.IsActive, c.CreatedAt, c.UpdatedAt,
        comp.CompanyID, comp.CompanyName, comp.Industry,
        (SELECT COUNT(*) FROM Shipment s WHERE s.CustomerID = c.CustomerID) as TotalShipments,
        (SELECT COUNT(*) FROM Shipment s WHERE s.CustomerID = c.CustomerID AND s.Status = 'Delivered') as DeliveredShipments
    FROM Customer c
    LEFT JOIN Company comp ON c.CustomerID = comp.CustomerID
    $whereClause
    ORDER BY c.$sortBy $sortOrder
    LIMIT ? OFFSET ?";
    
    $queryParams = array_merge($params, [$limit, $offset]);
    $customersStmt = $database->execute($customersQuery, $queryParams);
    $customers = $customersStmt->fetchAll();
    
    // Format customer data
    $formattedCustomers = [];
    foreach ($customers as $customer) {
        $formattedCustomers[] = [
            'id' => (int)$customer['CustomerID'],
            'first_name' => $customer['FirstName'],
            'last_name' => $customer['LastName'],
            'full_name' => $customer['FirstName'] . ' ' . $customer['LastName'],
            'email' => $customer['Email'],
            'phone' => $customer['Phone'],
            'address' => [
                'street' => $customer['Address'],
                'city' => $customer['City'],
                'state' => $customer['State'],
                'postal_code' => $customer['PostalCode'],
                'country' => $customer['Country']
            ],
            'date_of_birth' => $customer['DateOfBirth'],
            'is_active' => (bool)$customer['IsActive'],
            'company' => $customer['CompanyID'] ? [
                'id' => (int)$customer['CompanyID'],
                'name' => $customer['CompanyName'],
                'industry' => $customer['Industry']
            ] : null,
            'statistics' => [
                'total_shipments' => (int)$customer['TotalShipments'],
                'delivered_shipments' => (int)$customer['DeliveredShipments']
            ],
            'created_at' => $customer['CreatedAt'],
            'updated_at' => $customer['UpdatedAt']
        ];
    }
    
    // Calculate pagination info
    $totalPages = ceil($totalCount / $limit);
    $hasNextPage = $page < $totalPages;
    $hasPrevPage = $page > 1;
    
    // Log analytics
    $database->logAnalytics($userID, 'VIEW_CUSTOMERS', 'Customers', [
        'page' => $page,
        'limit' => $limit,
        'search' => $search,
        'total_results' => $totalCount
    ]);
    
    // Prepare response data
    $responseData = [
        'customers' => $formattedCustomers,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_count' => $totalCount,
            'per_page' => $limit,
            'has_next_page' => $hasNextPage,
            'has_prev_page' => $hasPrevPage
        ],
        'filters' => [
            'search' => $search,
            'status' => $status,
            'sort_by' => $sortBy,
            'sort_order' => $sortOrder
        ]
    ];
    
    ApiResponse::success($responseData, 'Customers retrieved successfully');
    
} catch (Exception $e) {
    error_log("List customers error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            SessionManager::getUserId(),
            'LIST_CUSTOMERS_ERROR',
            $e->getMessage(),
            $e->getTraceAsString()
        );
    }
    
    ApiResponse::serverError('An error occurred while retrieving customers');
}
?>
