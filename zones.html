<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Delivery Zones &mdash; ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="ZamSend delivery zones and coverage areas. Check if we deliver to your destination and view shipping zones worldwide." />
	<meta name="keywords" content="delivery zones, shipping areas, ZamSend coverage, international delivery" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">

	<style>
		.zones-hero {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 100px 0;
		}
		
		.zone-section {
			padding: 60px 0;
		}
		
		.zone-card {
			background: white;
			border-radius: 15px;
			box-shadow: 0 10px 30px rgba(0,0,0,0.1);
			padding: 30px;
			margin-bottom: 30px;
			transition: transform 0.3s ease;
		}
		
		.zone-card:hover {
			transform: translateY(-5px);
		}
		
		.zone-header {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
		}
		
		.zone-icon {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24px;
			color: white;
			margin-right: 20px;
		}
		
		.zone-1 { background: #4caf50; }
		.zone-2 { background: #2196f3; }
		.zone-3 { background: #ff9800; }
		.zone-4 { background: #9c27b0; }
		.zone-5 { background: #f44336; }
		.zone-6 { background: #607d8b; }
		
		.zone-title {
			color: #333;
			margin-bottom: 5px;
		}
		
		.zone-subtitle {
			color: #666;
			font-size: 14px;
		}
		
		.country-list {
			list-style: none;
			padding: 0;
			margin: 0;
		}
		
		.country-list li {
			padding: 8px 0;
			border-bottom: 1px solid #f0f0f0;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		
		.country-list li:last-child {
			border-bottom: none;
		}
		
		.delivery-time {
			background: #e3f2fd;
			color: #1976d2;
			padding: 4px 12px;
			border-radius: 15px;
			font-size: 12px;
			font-weight: 600;
		}
		
		.search-zone {
			background: #f8f9fa;
			padding: 40px;
			border-radius: 15px;
			margin-bottom: 40px;
		}
		
		.search-input {
			height: 50px;
			border-radius: 25px;
			border: 2px solid #e1e5e9;
			padding: 0 20px;
			font-size: 16px;
		}
		
		.search-input:focus {
			border-color: #667eea;
			box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
		}
		
		.btn-search {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 25px;
			color: white;
			font-weight: 600;
			padding: 12px 30px;
			height: 50px;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<nav class="fh5co-nav" role="navigation">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html">Home</a></li>
							<li><a href="services.html">Services</a></li>
							<li><a href="tracking.html">Track Package</a></li>
							<li class="has-dropdown active">
								<a href="pricing.html">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html">About</a></li>
							<li><a href="contact.html">Contact</a></li>
							<li class="btn-cta"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta"><a href="register.html"><span>Sign Up</span></a></li>
							<li class="btn-cta"><a href="ship-now.html"><span>Ship Now</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="zones-hero">
		<div class="container">
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center">
					<h1>Delivery Zones</h1>
					<p>ZamSend delivers to 45+ countries worldwide. Check our delivery zones and estimated transit times.</p>
				</div>
			</div>
		</div>
	</div>

	<div class="zone-section">
		<div class="container">
			<div class="search-zone">
				<div class="row">
					<div class="col-md-8 col-md-offset-2">
						<h3 style="text-align: center; margin-bottom: 30px;">Find Your Delivery Zone</h3>
						<div class="input-group">
							<input type="text" class="form-control search-input" placeholder="Enter country or city name..." id="zoneSearch">
							<span class="input-group-btn">
								<button class="btn btn-search" type="button" onclick="searchZone()">
									<i class="icon-search"></i> Search
								</button>
							</span>
						</div>
					</div>
				</div>
			</div>
			
			<div class="row">
				<div class="col-md-6">
					<div class="zone-card">
						<div class="zone-header">
							<div class="zone-icon zone-1">1</div>
							<div>
								<h4 class="zone-title">Zone 1 - Regional (Southern Africa)</h4>
								<p class="zone-subtitle">Neighboring countries with land borders</p>
							</div>
						</div>
						<ul class="country-list">
							<li>South Africa <span class="delivery-time">2-4 days</span></li>
							<li>Zimbabwe <span class="delivery-time">2-3 days</span></li>
							<li>Botswana <span class="delivery-time">2-4 days</span></li>
							<li>Namibia <span class="delivery-time">3-5 days</span></li>
							<li>Malawi <span class="delivery-time">2-4 days</span></li>
							<li>Mozambique <span class="delivery-time">3-5 days</span></li>
						</ul>
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="zone-card">
						<div class="zone-header">
							<div class="zone-icon zone-2">2</div>
							<div>
								<h4 class="zone-title">Zone 2 - Regional (East Africa)</h4>
								<p class="zone-subtitle">East African countries</p>
							</div>
						</div>
						<ul class="country-list">
							<li>Kenya <span class="delivery-time">3-6 days</span></li>
							<li>Tanzania <span class="delivery-time">3-6 days</span></li>
							<li>Uganda <span class="delivery-time">4-7 days</span></li>
							<li>Rwanda <span class="delivery-time">4-7 days</span></li>
							<li>Burundi <span class="delivery-time">5-8 days</span></li>
							<li>Ethiopia <span class="delivery-time">5-8 days</span></li>
						</ul>
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="zone-card">
						<div class="zone-header">
							<div class="zone-icon zone-3">3</div>
							<div>
								<h4 class="zone-title">Zone 3 - International (Asia)</h4>
								<p class="zone-subtitle">Asian countries</p>
							</div>
						</div>
						<ul class="country-list">
							<li>China <span class="delivery-time">5-10 days</span></li>
							<li>India <span class="delivery-time">5-10 days</span></li>
							<li>Japan <span class="delivery-time">6-12 days</span></li>
							<li>Singapore <span class="delivery-time">6-12 days</span></li>
							<li>Malaysia <span class="delivery-time">6-12 days</span></li>
							<li>Thailand <span class="delivery-time">7-14 days</span></li>
						</ul>
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="zone-card">
						<div class="zone-header">
							<div class="zone-icon zone-4">4</div>
							<div>
								<h4 class="zone-title">Zone 4 - International (Europe)</h4>
								<p class="zone-subtitle">European countries</p>
							</div>
						</div>
						<ul class="country-list">
							<li>United Kingdom <span class="delivery-time">7-14 days</span></li>
							<li>Germany <span class="delivery-time">7-14 days</span></li>
							<li>France <span class="delivery-time">7-14 days</span></li>
							<li>Netherlands <span class="delivery-time">7-14 days</span></li>
							<li>Italy <span class="delivery-time">8-15 days</span></li>
							<li>Spain <span class="delivery-time">8-15 days</span></li>
						</ul>
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="zone-card">
						<div class="zone-header">
							<div class="zone-icon zone-5">5</div>
							<div>
								<h4 class="zone-title">Zone 5 - International (Americas)</h4>
								<p class="zone-subtitle">North and South American countries</p>
							</div>
						</div>
						<ul class="country-list">
							<li>United States <span class="delivery-time">8-15 days</span></li>
							<li>Canada <span class="delivery-time">8-15 days</span></li>
							<li>Brazil <span class="delivery-time">10-18 days</span></li>
							<li>Mexico <span class="delivery-time">10-18 days</span></li>
							<li>Argentina <span class="delivery-time">12-20 days</span></li>
							<li>Chile <span class="delivery-time">12-20 days</span></li>
						</ul>
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="zone-card">
						<div class="zone-header">
							<div class="zone-icon zone-6">6</div>
							<div>
								<h4 class="zone-title">Zone 6 - International (Oceania)</h4>
								<p class="zone-subtitle">Australia and Pacific region</p>
							</div>
						</div>
						<ul class="country-list">
							<li>Australia <span class="delivery-time">10-18 days</span></li>
							<li>New Zealand <span class="delivery-time">12-20 days</span></li>
							<li>Fiji <span class="delivery-time">14-21 days</span></li>
							<li>Papua New Guinea <span class="delivery-time">14-21 days</span></li>
						</ul>
					</div>
				</div>
			</div>
			
			<div class="row" style="margin-top: 40px;">
				<div class="col-md-12">
					<div style="background: #e3f2fd; padding: 30px; border-radius: 15px; border-left: 5px solid #2196f3;">
						<h4 style="color: #1976d2; margin-bottom: 15px;"><i class="icon-info"></i> Important Notes</h4>
						<ul style="color: #555; margin: 0;">
							<li>Delivery times are estimates and may vary due to customs clearance, weather conditions, or other factors beyond our control.</li>
							<li>Express services are available for faster delivery to most destinations.</li>
							<li>Remote areas within countries may require additional delivery time.</li>
							<li>Some destinations may have restrictions on certain types of goods.</li>
							<li>Customs duties and taxes may apply and are the responsibility of the recipient.</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>

	<footer id="fh5co-footer" role="contentinfo">
		<div class="container">
			<div class="row copyright">
				<div class="col-md-12 text-center">
					<p>
						<small class="block">&copy; 2024 ZamSend Courier Services. All Rights Reserved.</small> 
					</p>
				</div>
			</div>
		</div>
	</footer>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	<!-- Main -->
	<script src="js/main.js"></script>
	
	<script>
		function searchZone() {
			const searchTerm = document.getElementById('zoneSearch').value.toLowerCase();
			const zoneCards = document.querySelectorAll('.zone-card');
			
			zoneCards.forEach(card => {
				const countries = card.querySelectorAll('.country-list li');
				let found = false;
				
				countries.forEach(country => {
					const countryName = country.textContent.toLowerCase();
					if (countryName.includes(searchTerm)) {
						found = true;
						country.style.backgroundColor = '#fff3cd';
					} else {
						country.style.backgroundColor = '';
					}
				});
				
				if (found || searchTerm === '') {
					card.style.display = 'block';
				} else {
					card.style.display = 'none';
				}
			});
		}
		
		// Reset search on input clear
		document.getElementById('zoneSearch').addEventListener('input', function() {
			if (this.value === '') {
				searchZone();
			}
		});
	</script>

	</body>
</html>
