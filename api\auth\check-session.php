<?php
/**
 * Session Check API
 * Validates current user session and returns user info
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Check if user is logged in and session is valid
    if (!SessionManager::isLoggedIn() || !SessionManager::checkTimeout()) {
        ApiResponse::error('Session expired or invalid', 401);
    }
    
    $userID = SessionManager::getUserId();
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get current user info
    $query = "SELECT UserID, Username, Email, FirstName, LastName, Role, IsActive, LastLogin 
              FROM Users 
              WHERE UserID = ? AND IsActive = 1";
    
    $stmt = $database->execute($query, [$userID]);
    $user = $stmt->fetch();
    
    if (!$user) {
        SessionManager::logout();
        ApiResponse::error('User not found or inactive', 401);
    }
    
    // Get user permissions
    $permQuery = "SELECT p.PermissionName, p.Module, p.Action 
                  FROM UserPermissions up 
                  JOIN Permissions p ON up.PermissionID = p.PermissionID 
                  WHERE up.UserID = ?";
    
    $permStmt = $database->execute($permQuery, [$userID]);
    $permissions = $permStmt->fetchAll();
    
    // Get unread notifications count
    $notifQuery = "SELECT COUNT(*) as unread_count 
                   FROM Notifications 
                   WHERE UserID = ? AND IsRead = 0";
    
    $notifStmt = $database->execute($notifQuery, [$userID]);
    $notifResult = $notifStmt->fetch();
    
    // Prepare response data
    $responseData = [
        'user' => [
            'id' => $user['UserID'],
            'username' => $user['Username'],
            'email' => $user['Email'],
            'first_name' => $user['FirstName'],
            'last_name' => $user['LastName'],
            'role' => $user['Role'],
            'last_login' => $user['LastLogin']
        ],
        'permissions' => $permissions,
        'unread_notifications' => $notifResult['unread_count'],
        'session_info' => [
            'login_time' => $_SESSION['login_time'] ?? null,
            'last_activity' => $_SESSION['last_activity'] ?? null,
            'timeout_minutes' => 15
        ]
    ];
    
    ApiResponse::success($responseData, 'Session valid');
    
} catch (Exception $e) {
    error_log("Session check error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            SessionManager::getUserId(),
            'SESSION_ERROR',
            $e->getMessage(),
            $e->getTraceAsString()
        );
    }
    
    ApiResponse::serverError('An error occurred while checking session');
}
?>
