<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Login - ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="Login to ZamSend courier services platform to manage shipments, track packages, and access your account." />
	<meta name="keywords" content="login, courier services, shipment management, package tracking, ZamSend" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">
	
	<!-- Custom login styles -->
	<style>
		.login-hero {
			position: relative;
			min-height: 100vh;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 140px 0 80px 0;
		}

		.login-hero video {
			position: fixed;
			top: 0;
			left: 0;
			min-width: 100%;
			min-height: 100%;
			width: auto;
			height: auto;
			z-index: -2;
			object-fit: cover;
		}

		.login-hero::before {
			content: '';
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg, rgba(102, 126, 234, 0.85) 0%, rgba(118, 75, 162, 0.85) 100%);
			z-index: -1;
		}

		.login-container {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.login-card {
			background: rgba(255, 255, 255, 0.98);
			backdrop-filter: blur(15px);
			border-radius: 20px;
			box-shadow: 0 25px 50px rgba(0,0,0,0.15);
			padding: 40px;
			max-width: 450px;
			width: 100%;
			margin: 0 20px;
		}
		
		.login-header {
			text-align: center;
			margin-bottom: 30px;
		}
		
		.login-header h2 {
			color: #333;
			margin-bottom: 10px;
		}
		
		.login-header p {
			color: #666;
			margin-bottom: 0;
		}
		
		.form-group {
			margin-bottom: 20px;
		}
		
		.form-control {
			height: 50px;
			border-radius: 8px;
			border: 2px solid #e1e5e9;
			padding: 0 15px;
			font-size: 16px;
			transition: all 0.3s ease;
		}
		
		.form-control:focus {
			border-color: #667eea;
			box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
		}
		
		.btn-login {
			width: 100%;
			height: 50px;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 8px;
			color: white;
			font-size: 16px;
			font-weight: 600;
			transition: all 0.3s ease;
		}
		
		.btn-login:hover {
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
		}
		
		.two-factor-section {
			display: none;
			margin-top: 20px;
			padding: 20px;
			background: #f8f9fa;
			border-radius: 8px;
		}
		
		.demo-code {
			background: #e3f2fd;
			padding: 10px;
			border-radius: 5px;
			margin: 10px 0;
			font-family: monospace;
			font-size: 18px;
			text-align: center;
			font-weight: bold;
		}
		
		.alert {
			margin-bottom: 20px;
			border-radius: 8px;
		}
		
		.loading {
			display: none;
		}
		
		.loading .spinner-border {
			width: 20px;
			height: 20px;
		}
		
		.back-to-home {
			text-align: center;
			margin-top: 20px;
		}
		
		.back-to-home a {
			color: #667eea;
			text-decoration: none;
		}
		
		.back-to-home a:hover {
			text-decoration: underline;
		}

		/* Responsive adjustments */
		@media (max-width: 768px) {
			.login-hero {
				padding: 120px 0 60px 0;
			}

			.login-card {
				margin: 0 15px;
				padding: 30px;
			}

			.login-header h2 {
				font-size: 24px;
			}
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>

	<div id="page">
	<nav class="fh5co-nav" role="navigation" style="position: absolute; top: 0; width: 100%; z-index: 1000; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num" style="color: white;">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook" style="color: white;"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter" style="color: white;"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn" style="color: white;"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp" style="color: white;"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html" style="color: white;">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html" style="color: white;">Home</a></li>
							<li><a href="services.html" style="color: white;">Services</a></li>
							<li><a href="tracking.html" style="color: white;">Track Package</a></li>
							<li class="has-dropdown">
								<a href="pricing.html" style="color: white;">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html" style="color: white;">About</a></li>
							<li><a href="contact.html" style="color: white;">Contact</a></li>
							<li class="btn-cta active"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta"><a href="register.html"><span>Sign Up</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="login-hero">
		<video autoplay muted loop>
			<source src="videos/back.mp4" type="video/mp4">
			Your browser does not support the video tag.
		</video>

		<div class="login-container">
			<div class="login-card">
				<div class="login-header">
					<h2>Welcome to ZamSend</h2>
					<p>Sign in to your account</p>
					<p><a href="register.html" style="color: #667eea; text-decoration: none;">Don't have an account? Sign up here</a></p>
				</div>
						
						<div id="alert-container"></div>
						
						<form id="loginForm">
							<div class="form-group">
								<label for="username">Username or Email</label>
								<input type="text" class="form-control" id="username" name="username" required>
							</div>
							
							<div class="form-group">
								<label for="password">Password</label>
								<input type="password" class="form-control" id="password" name="password" required>
							</div>
							
							<div class="two-factor-section" id="twoFactorSection">
								<h5>Two-Factor Authentication</h5>
								<p>Enter the 6-digit code:</p>
								<div class="demo-code" id="demoCode" style="display: none;"></div>
								<div class="form-group">
									<input type="text" class="form-control" id="twoFactorCode" name="two_factor_code" placeholder="000000" maxlength="6">
								</div>
							</div>
							
							<button type="submit" class="btn btn-login">
								<span class="login-text">Sign In</span>
								<span class="loading">
									<span class="spinner-border spinner-border-sm" role="status"></span>
									Signing in...
								</span>
							</button>
						</form>
						
						<div class="back-to-home">
							<a href="index.html">&larr; Back to Home</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	
	<script>
		$(document).ready(function() {
			$('#loginForm').on('submit', function(e) {
				e.preventDefault();
				
				const formData = {
					username: $('#username').val(),
					password: $('#password').val(),
					two_factor_code: $('#twoFactorCode').val()
				};
				
				// Show loading state
				$('.login-text').hide();
				$('.loading').show();
				$('#loginForm button').prop('disabled', true);
				
				$.ajax({
					url: 'api/auth/login.php',
					method: 'POST',
					data: JSON.stringify(formData),
					contentType: 'application/json',
					success: function(response) {
						if (response.success) {
							if (response.data && response.data.requires_2fa) {
								// Show 2FA section
								$('#twoFactorSection').show();
								if (response.data.demo_code) {
									$('#demoCode').text('Demo Code: ' + response.data.demo_code).show();
								}
								showAlert('Please enter the two-factor authentication code.', 'info');
							} else {
								// Login successful
								showAlert('Login successful! Redirecting...', 'success');
								setTimeout(function() {
									// Redirect based on user role
									if (response.data.user.role === 'SuperAdmin') {
										window.location.href = 'admin/dashboard.html';
									} else {
										window.location.href = 'dashboard.html';
									}
								}, 1500);
							}
						}
					},
					error: function(xhr) {
						const response = JSON.parse(xhr.responseText);
						showAlert(response.message || 'Login failed. Please try again.', 'danger');
					},
					complete: function() {
						// Hide loading state
						$('.login-text').show();
						$('.loading').hide();
						$('#loginForm button').prop('disabled', false);
					}
				});
			});
			
			function showAlert(message, type) {
				const alertHtml = `
					<div class="alert alert-${type} alert-dismissible fade in" role="alert">
						<button type="button" class="close" data-dismiss="alert">
							<span>&times;</span>
						</button>
						${message}
					</div>
				`;
				$('#alert-container').html(alertHtml);
			}
		});
	</script>
	</div>
	</div>
</div>

	</body>
</html>
