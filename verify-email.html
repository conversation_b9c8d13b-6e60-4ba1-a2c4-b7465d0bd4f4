<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Verify Email &mdash; ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="Verify your email address to activate your ZamSend courier services account." />
	<meta name="keywords" content="email verification, account activation, ZamSend" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">

	<!-- Custom verification styles -->
	<style>
		.verify-hero {
			position: relative;
			height: 100vh;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.verify-hero video {
			position: absolute;
			top: 50%;
			left: 50%;
			min-width: 100%;
			min-height: 100%;
			width: auto;
			height: auto;
			transform: translateX(-50%) translateY(-50%);
			z-index: -2;
		}
		
		.verify-hero::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
			z-index: -1;
		}
		
		.verify-container {
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(10px);
			border-radius: 20px;
			padding: 40px;
			box-shadow: 0 20px 40px rgba(0,0,0,0.1);
			max-width: 500px;
			width: 100%;
			margin: 0 20px;
			text-align: center;
		}
		
		.verify-icon {
			width: 80px;
			height: 80px;
			border-radius: 50%;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto 20px;
			font-size: 32px;
			color: white;
		}
		
		.verify-icon.success {
			background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		}
		
		.verify-icon.error {
			background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
		}
		
		.verify-header h1 {
			color: #333;
			margin-bottom: 10px;
			font-size: 28px;
		}
		
		.verify-header p {
			color: #666;
			margin-bottom: 30px;
			font-size: 16px;
		}
		
		.btn-verify {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 25px;
			color: white;
			font-size: 16px;
			font-weight: 600;
			padding: 12px 30px;
			margin: 10px;
			transition: all 0.3s ease;
			text-decoration: none;
			display: inline-block;
		}
		
		.btn-verify:hover {
			transform: translateY(-2px);
			box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
			color: white;
			text-decoration: none;
		}
		
		.btn-secondary {
			background: #6c757d;
		}
		
		.btn-secondary:hover {
			background: #5a6268;
			box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
		}
		
		.loading {
			display: none;
		}
		
		.loading .spinner-border {
			width: 20px;
			height: 20px;
		}
		
		.alert {
			border-radius: 10px;
			margin-bottom: 20px;
		}
		
		.verification-status {
			display: none;
		}
		
		.verification-status.show {
			display: block;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<nav class="fh5co-nav" role="navigation" style="position: absolute; top: 0; width: 100%; z-index: 1000; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num" style="color: white;">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook" style="color: white;"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter" style="color: white;"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn" style="color: white;"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp" style="color: white;"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html" style="color: white;">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html" style="color: white;">Home</a></li>
							<li><a href="services.html" style="color: white;">Services</a></li>
							<li><a href="tracking.html" style="color: white;">Track Package</a></li>
							<li class="has-dropdown">
								<a href="pricing.html" style="color: white;">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html" style="color: white;">About</a></li>
							<li><a href="contact.html" style="color: white;">Contact</a></li>
							<li class="btn-cta"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta"><a href="register.html"><span>Sign Up</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="verify-hero">
		<video autoplay muted loop>
			<source src="videos/back.mp4" type="video/mp4">
			Your browser does not support the video tag.
		</video>
		
		<div class="verify-container">
			<!-- Loading State -->
			<div id="loading-state" class="verification-status show">
				<div class="verify-icon">
					<span class="spinner-border" role="status"></span>
				</div>
				<div class="verify-header">
					<h1>Verifying Email</h1>
					<p>Please wait while we verify your email address...</p>
				</div>
			</div>
			
			<!-- Success State -->
			<div id="success-state" class="verification-status">
				<div class="verify-icon success">
					<i class="icon-check"></i>
				</div>
				<div class="verify-header">
					<h1>Email Verified!</h1>
					<p>Your email address has been successfully verified. Your account is now active and ready to use.</p>
				</div>
				<div>
					<a href="login.html" class="btn-verify">Sign In to Your Account</a>
					<a href="index.html" class="btn-verify btn-secondary">Go to Homepage</a>
				</div>
			</div>
			
			<!-- Error State -->
			<div id="error-state" class="verification-status">
				<div class="verify-icon error">
					<i class="icon-close"></i>
				</div>
				<div class="verify-header">
					<h1>Verification Failed</h1>
					<p id="error-message">The verification link is invalid or has expired. Please request a new verification email.</p>
				</div>
				<div>
					<button onclick="resendVerification()" class="btn-verify">
						<span class="resend-text">Resend Verification Email</span>
						<span class="loading">
							<span class="spinner-border spinner-border-sm" role="status"></span>
							Sending...
						</span>
					</button>
					<a href="register.html" class="btn-verify btn-secondary">Create New Account</a>
				</div>
			</div>
			
			<!-- Already Verified State -->
			<div id="already-verified-state" class="verification-status">
				<div class="verify-icon success">
					<i class="icon-check"></i>
				</div>
				<div class="verify-header">
					<h1>Already Verified</h1>
					<p>This email address has already been verified. You can sign in to your account.</p>
				</div>
				<div>
					<a href="login.html" class="btn-verify">Sign In to Your Account</a>
					<a href="index.html" class="btn-verify btn-secondary">Go to Homepage</a>
				</div>
			</div>
		</div>
	</div>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	
	<script>
		$(document).ready(function() {
			// Get token from URL
			const urlParams = new URLSearchParams(window.location.search);
			const token = urlParams.get('token');
			
			if (!token) {
				showError('No verification token provided');
				return;
			}
			
			// Verify the token
			verifyEmail(token);
		});
		
		function verifyEmail(token) {
			$.ajax({
				url: 'api/auth/verify-email.php',
				method: 'POST',
				data: JSON.stringify({ token: token }),
				contentType: 'application/json',
				success: function(response) {
					if (response.success) {
						if (response.data.already_verified) {
							showAlreadyVerified();
						} else {
							showSuccess();
						}
					}
				},
				error: function(xhr) {
					const response = JSON.parse(xhr.responseText);
					showError(response.message || 'Verification failed');
				}
			});
		}
		
		function showSuccess() {
			$('.verification-status').removeClass('show');
			$('#success-state').addClass('show');
		}
		
		function showError(message) {
			$('#error-message').text(message);
			$('.verification-status').removeClass('show');
			$('#error-state').addClass('show');
		}
		
		function showAlreadyVerified() {
			$('.verification-status').removeClass('show');
			$('#already-verified-state').addClass('show');
		}
		
		function resendVerification() {
			const urlParams = new URLSearchParams(window.location.search);
			const token = urlParams.get('token');
			
			$('.resend-text').hide();
			$('.loading').show();
			$('button').prop('disabled', true);
			
			$.ajax({
				url: 'api/auth/resend-verification.php',
				method: 'POST',
				data: JSON.stringify({ token: token }),
				contentType: 'application/json',
				success: function(response) {
					if (response.success) {
						alert('Verification email sent successfully! Please check your inbox.');
					}
				},
				error: function(xhr) {
					const response = JSON.parse(xhr.responseText);
					alert(response.message || 'Failed to send verification email');
				},
				complete: function() {
					$('.resend-text').show();
					$('.loading').hide();
					$('button').prop('disabled', false);
				}
			});
		}
	</script>

	</body>
</html>
