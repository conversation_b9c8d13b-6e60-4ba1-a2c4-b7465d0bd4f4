<?php
/**
 * User Authentication API
 * Handles user login with role-based access control
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST; // Fallback to form data
    }
    
    // Validate required fields
    if (empty($input['username']) || empty($input['password'])) {
        ApiResponse::error('Username and password are required');
    }
    
    $username = Database::sanitize($input['username']);
    $password = $input['password'];
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get user from database
    $query = "SELECT UserID, Username, Email, PasswordHash, FirstName, LastName, Role, IsActive, TwoFactorEnabled, LastLogin 
              FROM Users 
              WHERE (Username = ? OR Email = ?) AND IsActive = 1";
    
    $stmt = $database->execute($query, [$username, $username]);
    $user = $stmt->fetch();
    
    if (!$user) {
        // Log failed login attempt
        $database->logError(null, 'LOGIN_FAILED', "Failed login attempt for username: $username");
        ApiResponse::error('Invalid username or password', 401);
    }
    
    // Verify password
    if (!password_verify($password, $user['PasswordHash'])) {
        // Log failed login attempt
        $database->logError($user['UserID'], 'LOGIN_FAILED', "Invalid password for user: $username");
        ApiResponse::error('Invalid username or password', 401);
    }
    
    // Check if 2FA is enabled
    if ($user['TwoFactorEnabled']) {
        // For demo purposes, we'll simulate 2FA with a simple code
        if (empty($input['two_factor_code'])) {
            // Generate and store 2FA code in session for demo
            SessionManager::start();
            $twoFactorCode = str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
            $_SESSION['pending_2fa'] = [
                'user_id' => $user['UserID'],
                'code' => $twoFactorCode,
                'expires' => time() + 300 // 5 minutes
            ];
            
            // In a real system, this would be sent via email/SMS
            ApiResponse::success([
                'requires_2fa' => true,
                'demo_code' => $twoFactorCode // Only for demo purposes
            ], 'Two-factor authentication required');
        } else {
            // Verify 2FA code
            SessionManager::start();
            if (!isset($_SESSION['pending_2fa']) || 
                $_SESSION['pending_2fa']['user_id'] != $user['UserID'] ||
                $_SESSION['pending_2fa']['expires'] < time() ||
                $_SESSION['pending_2fa']['code'] != $input['two_factor_code']) {
                
                unset($_SESSION['pending_2fa']);
                ApiResponse::error('Invalid or expired two-factor code', 401);
            }
            
            unset($_SESSION['pending_2fa']);
        }
    }
    
    // Get user permissions
    $permQuery = "SELECT p.PermissionName, p.Module, p.Action 
                  FROM UserPermissions up 
                  JOIN Permissions p ON up.PermissionID = p.PermissionID 
                  WHERE up.UserID = ?";
    
    $permStmt = $database->execute($permQuery, [$user['UserID']]);
    $permissions = $permStmt->fetchAll();
    
    // Update last login
    $updateQuery = "UPDATE Users SET LastLogin = NOW() WHERE UserID = ?";
    $database->execute($updateQuery, [$user['UserID']]);
    
    // Set session
    SessionManager::setUser(
        $user['UserID'],
        $user['Username'],
        $user['Role'],
        $user['Email']
    );
    
    // Log successful login
    $database->logAnalytics($user['UserID'], 'LOGIN', 'Authentication', [
        'login_time' => date('Y-m-d H:i:s'),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    // Prepare response data
    $responseData = [
        'user' => [
            'id' => $user['UserID'],
            'username' => $user['Username'],
            'email' => $user['Email'],
            'first_name' => $user['FirstName'],
            'last_name' => $user['LastName'],
            'role' => $user['Role'],
            'last_login' => $user['LastLogin']
        ],
        'permissions' => $permissions,
        'session_timeout' => 15 // minutes
    ];
    
    ApiResponse::success($responseData, 'Login successful');
    
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            SessionManager::getUserId(),
            'LOGIN_ERROR',
            $e->getMessage(),
            $e->getTraceAsString()
        );
    }
    
    ApiResponse::serverError('An error occurred during login');
}
?>
