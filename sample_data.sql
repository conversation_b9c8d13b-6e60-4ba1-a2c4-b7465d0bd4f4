-- ZamShipment Sample Data
-- Insert sample data as specified in README.txt
-- 1 super admin, 5 sub-admins, 10 customers, 20 shipments, 50 tracking events, 15 deliveries, 100 audit logs

USE courier_db;

-- Insert System Settings
INSERT INTO SystemSettings (SettingName, SettingValue, Description) VALUES
('company_name', 'ZamSend Courier Services', 'Company name displayed on the website'),
('company_email', '<EMAIL>', 'Main company email address'),
('company_phone', '+260-**********', 'Main company phone number'),
('company_address', '123 Independence Avenue, Lusaka, Zambia', 'Company physical address'),
('max_package_weight', '50', 'Maximum package weight in kg'),
('session_timeout', '15', 'Session timeout in minutes'),
('enable_2fa', '1', 'Enable two-factor authentication'),
('maintenance_mode', '0', 'Enable maintenance mode');

-- Insert Permissions
INSERT INTO Permissions (PermissionName, Description, Module, Action) VALUES
-- Customer Management
('customers_view', 'View customer information', 'Customers', 'View'),
('customers_create', 'Create new customers', 'Customers', 'Create'),
('customers_edit', 'Edit customer information', 'Customers', 'Edit'),
('customers_delete', 'Delete customers', 'Customers', 'Delete'),

-- Shipment Management
('shipments_view', 'View shipment information', 'Shipments', 'View'),
('shipments_create', 'Create new shipments', 'Shipments', 'Create'),
('shipments_edit', 'Edit shipment information', 'Shipments', 'Edit'),
('shipments_delete', 'Delete shipments', 'Shipments', 'Delete'),

-- Tracking Management
('tracking_view', 'View tracking information', 'Tracking', 'View'),
('tracking_update', 'Update tracking status', 'Tracking', 'Edit'),

-- Delivery Management
('deliveries_view', 'View delivery information', 'Deliveries', 'View'),
('deliveries_create', 'Create delivery records', 'Deliveries', 'Create'),
('deliveries_edit', 'Edit delivery information', 'Deliveries', 'Edit'),

-- Reports
('reports_view', 'View reports', 'Reports', 'View'),
('reports_create', 'Create custom reports', 'Reports', 'Create'),

-- User Management (Super Admin only)
('users_view', 'View user information', 'Users', 'View'),
('users_create', 'Create new users', 'Users', 'Create'),
('users_edit', 'Edit user information', 'Users', 'Edit'),
('users_delete', 'Delete users', 'Users', 'Delete'),

-- System Management
('system_settings', 'Manage system settings', 'System', 'Edit'),
('audit_logs', 'View audit logs', 'System', 'View');

-- Insert Users (1 Super Admin, 5 Sub-Admins)
INSERT INTO Users (Username, Email, PasswordHash, FirstName, LastName, Phone, Role, TwoFactorEnabled) VALUES
-- Super Admin (password: SuperAdmin123!)
('superadmin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', '+260-977-123456', 'SuperAdmin', TRUE),

-- Sub-Admins (password: SubAdmin123!)
('subadmin1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Mwanza', '+260-977-234567', 'SubAdmin', FALSE),
('subadmin2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mary', 'Banda', '+260-977-345678', 'SubAdmin', FALSE),
('subadmin3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Peter', 'Phiri', '+260-977-456789', 'SubAdmin', FALSE),
('subadmin4', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Grace', 'Tembo', '+260-977-567890', 'SubAdmin', FALSE),
('subadmin5', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'David', 'Sakala', '+260-977-678901', 'SubAdmin', FALSE),

-- Customer Service Staff
('cs_staff1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alice', 'Mulenga', '+260-977-789012', 'CustomerService', FALSE),
('cs_staff2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'James', 'Chanda', '+260-977-890123', 'CustomerService', FALSE),

-- Logistics Staff
('logistics1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Robert', 'Zulu', '+260-977-901234', 'Logistics', FALSE),
('logistics2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Susan', 'Mwale', '+260-977-012345', 'Logistics', FALSE);

-- Grant all permissions to Super Admin
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 1, PermissionID, 1 FROM Permissions;

-- Grant specific permissions to Sub-Admins
-- SubAdmin1: Customer and Shipment management
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 2, PermissionID, 1 FROM Permissions 
WHERE PermissionName IN ('customers_view', 'customers_create', 'customers_edit', 'shipments_view', 'shipments_create', 'shipments_edit', 'tracking_view', 'reports_view');

-- SubAdmin2: Full shipment and delivery management
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 3, PermissionID, 1 FROM Permissions 
WHERE PermissionName LIKE 'shipments_%' OR PermissionName LIKE 'deliveries_%' OR PermissionName LIKE 'tracking_%' OR PermissionName = 'reports_view';

-- SubAdmin3: Reports and analytics
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 4, PermissionID, 1 FROM Permissions 
WHERE PermissionName LIKE 'reports_%' OR PermissionName = 'audit_logs' OR PermissionName LIKE '%_view';

-- SubAdmin4: Customer service focused
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 5, PermissionID, 1 FROM Permissions 
WHERE PermissionName IN ('customers_view', 'customers_edit', 'shipments_view', 'tracking_view', 'tracking_update', 'deliveries_view');

-- SubAdmin5: Logistics focused
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 6, PermissionID, 1 FROM Permissions 
WHERE PermissionName IN ('shipments_view', 'shipments_edit', 'tracking_view', 'tracking_update', 'deliveries_view', 'deliveries_create', 'deliveries_edit');

-- Customer Service permissions
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 7, PermissionID, 1 FROM Permissions 
WHERE PermissionName IN ('customers_view', 'customers_create', 'shipments_view', 'shipments_create', 'tracking_view', 'reports_view');

INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 8, PermissionID, 1 FROM Permissions 
WHERE PermissionName IN ('customers_view', 'customers_create', 'shipments_view', 'shipments_create', 'tracking_view', 'reports_view');

-- Logistics permissions
INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 9, PermissionID, 1 FROM Permissions 
WHERE PermissionName IN ('shipments_view', 'tracking_view', 'tracking_update', 'deliveries_view', 'deliveries_create', 'deliveries_edit');

INSERT INTO UserPermissions (UserID, PermissionID, GrantedBy)
SELECT 10, PermissionID, 1 FROM Permissions
WHERE PermissionName IN ('shipments_view', 'tracking_view', 'tracking_update', 'deliveries_view', 'deliveries_create', 'deliveries_edit');

-- Insert 10 Customers
INSERT INTO Customer (FirstName, LastName, Email, Phone, Address, City, State, PostalCode, Country, DateOfBirth) VALUES
('Michael', 'Chisanga', '<EMAIL>', '+260-**********', '123 Cairo Road', 'Lusaka', 'Lusaka Province', '10101', 'Zambia', '1985-03-15'),
('Sarah', 'Mubanga', '<EMAIL>', '+260-**********', '456 Independence Avenue', 'Lusaka', 'Lusaka Province', '10102', 'Zambia', '1990-07-22'),
('Joseph', 'Katongo', '<EMAIL>', '+260-**********', '789 Great East Road', 'Lusaka', 'Lusaka Province', '10103', 'Zambia', '1988-11-08'),
('Elizabeth', 'Mwanza', '<EMAIL>', '+260-977-444444', '321 Kafue Road', 'Lusaka', 'Lusaka Province', '10104', 'Zambia', '1992-05-14'),
('Patrick', 'Lungu', '<EMAIL>', '+260-**********', '654 Chiparamba Road', 'Kitwe', 'Copperbelt Province', '20101', 'Zambia', '1987-09-30'),
('Grace', 'Mulenga', '<EMAIL>', '+260-977-666666', '987 Ndola Road', 'Kitwe', 'Copperbelt Province', '20102', 'Zambia', '1991-12-03'),
('Daniel', 'Phiri', '<EMAIL>', '+260-**********', '147 Broadway', 'Ndola', 'Copperbelt Province', '20201', 'Zambia', '1989-04-18'),
('Ruth', 'Banda', '<EMAIL>', '+260-**********', '258 Kabwe Road', 'Kabwe', 'Central Province', '30101', 'Zambia', '1993-08-25'),
('Samuel', 'Tembo', '<EMAIL>', '+260-**********', '369 Livingstone Road', 'Livingstone', 'Southern Province', '40101', 'Zambia', '1986-01-12'),
('Mary', 'Zulu', '<EMAIL>', '+260-**********', '741 Mongu Road', 'Mongu', 'Western Province', '50101', 'Zambia', '1994-06-07');

-- Insert Company details for some customers
INSERT INTO Company (CustomerID, CompanyName, RegistrationNumber, TaxID, Industry, Website, BillingAddress, ContactPerson, ContactEmail, ContactPhone) VALUES
(1, 'Chisanga Trading Ltd', 'REG001234', 'TAX001234', 'Import/Export', 'www.chisangatrading.com', '123 Cairo Road, Lusaka', 'Michael Chisanga', '<EMAIL>', '+260-**********'),
(3, 'Katongo Logistics', 'REG005678', 'TAX005678', 'Logistics', 'www.katongologistics.com', '789 Great East Road, Lusaka', 'Joseph Katongo', '<EMAIL>', '+260-**********'),
(5, 'Lungu Mining Supplies', 'REG009012', 'TAX009012', 'Mining', 'www.lungumining.com', '654 Chiparamba Road, Kitwe', 'Patrick Lungu', '<EMAIL>', '+260-**********');

-- Insert 20 Shipments
INSERT INTO Shipment (TrackingNumber, CustomerID, SenderName, SenderAddress, SenderPhone, RecipientName, RecipientAddress, RecipientPhone, ShipmentType, DeliveryMode, Weight, Dimensions, DeclaredValue, ShippingCost, Status, Priority, SpecialInstructions, EstimatedDeliveryDate) VALUES
('PKG-2024-001', 1, 'Michael Chisanga', '123 Cairo Road, Lusaka', '+260-**********', 'John Smith', '456 Main Street, Johannesburg, South Africa', '+27-11-123-4567', 'Express', 'Air', 2.50, '30x20x15', 500.00, 75.00, 'Delivered', 'High', 'Handle with care', '2024-01-15'),
('PKG-2024-002', 2, 'Sarah Mubanga', '456 Independence Avenue, Lusaka', '+260-**********', 'Jane Doe', '789 Oak Street, Nairobi, Kenya', '+254-20-123-4567', 'Standard', 'Road', 5.00, '40x30x20', 200.00, 45.00, 'Delivered', 'Medium', NULL, '2024-01-18'),
('PKG-2024-003', 3, 'Joseph Katongo', '789 Great East Road, Lusaka', '+260-**********', 'Bob Johnson', '321 Pine Avenue, Harare, Zimbabwe', '+263-4-123-4567', 'Express', 'Air', 1.20, '25x15x10', 800.00, 65.00, 'In Transit', 'High', 'Fragile items', '2024-01-20'),
('PKG-2024-004', 4, 'Elizabeth Mwanza', '321 Kafue Road, Lusaka', '+260-977-444444', 'Alice Brown', '654 Elm Street, Gaborone, Botswana', '+267-3-123-4567', 'Standard', 'Road', 8.50, '50x40x30', 150.00, 55.00, 'Out for Delivery', 'Medium', NULL, '2024-01-22'),
('PKG-2024-005', 5, 'Patrick Lungu', '654 Chiparamba Road, Kitwe', '+260-**********', 'Charlie Wilson', '987 Maple Drive, Dar es Salaam, Tanzania', '+255-22-123-4567', 'Special Handling', 'Ocean', 15.00, '60x50x40', 1200.00, 120.00, 'Picked Up', 'Urgent', 'Temperature sensitive', '2024-01-25'),
('PKG-2024-006', 6, 'Grace Mulenga', '987 Ndola Road, Kitwe', '+260-977-666666', 'Diana Davis', '147 Cedar Lane, Windhoek, Namibia', '+264-61-123-4567', 'Express', 'Air', 3.75, '35x25x18', 600.00, 80.00, 'In Transit', 'High', NULL, '2024-01-23'),
('PKG-2024-007', 7, 'Daniel Phiri', '147 Broadway, Ndola', '+260-**********', 'Edward Miller', '258 Birch Road, Maputo, Mozambique', '+258-21-123-4567', 'Standard', 'Road', 6.20, '45x35x25', 300.00, 50.00, 'Pending', 'Medium', NULL, '2024-01-26'),
('PKG-2024-008', 8, 'Ruth Banda', '258 Kabwe Road, Kabwe', '+260-**********', 'Fiona Garcia', '369 Spruce Street, Lilongwe, Malawi', '+265-1-123-4567', 'Express', 'Air', 2.10, '28x18x12', 450.00, 70.00, 'Picked Up', 'High', 'Documents only', '2024-01-24'),
('PKG-2024-009', 9, 'Samuel Tembo', '369 Livingstone Road, Livingstone', '+260-**********', 'George Martinez', '741 Willow Avenue, Kigali, Rwanda', '+250-252-123-456', 'Standard', 'Road', 4.80, '38x28x22', 250.00, 48.00, 'In Transit', 'Medium', NULL, '2024-01-27'),
('PKG-2024-010', 10, 'Mary Zulu', '741 Mongu Road, Mongu', '+260-**********', 'Helen Rodriguez', '852 Poplar Drive, Kampala, Uganda', '+256-41-123-4567', 'Express', 'Air', 1.90, '26x16x11', 700.00, 68.00, 'Delivered', 'High', 'Urgent delivery', '2024-01-21'),
('PKG-2024-011', 1, 'Michael Chisanga', '123 Cairo Road, Lusaka', '+260-**********', 'Ivan Thompson', '963 Ash Street, Lagos, Nigeria', '+234-1-123-4567', 'Standard', 'Ocean', 12.00, '55x45x35', 400.00, 85.00, 'Pending', 'Medium', NULL, '2024-01-30'),
('PKG-2024-012', 2, 'Sarah Mubanga', '456 Independence Avenue, Lusaka', '+260-**********', 'Julia Anderson', '174 Hickory Lane, Accra, Ghana', '+233-30-123-4567', 'Express', 'Air', 3.25, '32x22x16', 550.00, 75.00, 'Picked Up', 'High', NULL, '2024-01-25'),
('PKG-2024-013', 3, 'Joseph Katongo', '789 Great East Road, Lusaka', '+260-**********', 'Kevin White', '285 Chestnut Road, Abidjan, Ivory Coast', '+225-20-123-456', 'Standard', 'Road', 7.40, '48x38x28', 320.00, 52.00, 'In Transit', 'Medium', NULL, '2024-01-28'),
('PKG-2024-014', 4, 'Elizabeth Mwanza', '321 Kafue Road, Lusaka', '+260-977-444444', 'Laura Clark', '396 Walnut Avenue, Dakar, Senegal', '+221-33-123-4567', 'Express', 'Air', 2.80, '30x20x14', 480.00, 72.00, 'Out for Delivery', 'High', 'Signature required', '2024-01-26'),
('PKG-2024-015', 5, 'Patrick Lungu', '654 Chiparamba Road, Kitwe', '+260-**********', 'Mark Lewis', '507 Sycamore Drive, Bamako, Mali', '+223-20-123-456', 'Special Handling', 'Air', 4.60, '36x26x20', 900.00, 95.00, 'Pending', 'Urgent', 'Perishable goods', '2024-01-29'),
('PKG-2024-016', 6, 'Grace Mulenga', '987 Ndola Road, Kitwe', '+260-977-666666', 'Nancy Hall', '618 Magnolia Street, Ouagadougou, Burkina Faso', '+226-25-123-456', 'Standard', 'Road', 9.10, '52x42x32', 180.00, 58.00, 'Picked Up', 'Medium', NULL, '2024-01-31'),
('PKG-2024-017', 7, 'Daniel Phiri', '147 Broadway, Ndola', '+260-**********', 'Oscar Young', '729 Dogwood Lane, Niamey, Niger', '+227-20-123-456', 'Express', 'Air', 1.75, '24x14x9', 650.00, 66.00, 'In Transit', 'High', NULL, '2024-01-27'),
('PKG-2024-018', 8, 'Ruth Banda', '258 Kabwe Road, Kabwe', '+260-**********', 'Paula King', '840 Redwood Road, N\'Djamena, Chad', '+235-22-123-456', 'Standard', 'Road', 6.80, '46x36x26', 280.00, 51.00, 'Delivered', 'Medium', NULL, '2024-01-24'),
('PKG-2024-019', 9, 'Samuel Tembo', '369 Livingstone Road, Livingstone', '+260-**********', 'Quincy Wright', '951 Sequoia Avenue, Bangui, Central African Republic', '+236-21-123-456', 'Express', 'Air', 3.40, '33x23x17', 520.00, 74.00, 'Out for Delivery', 'High', NULL, '2024-01-28'),
('PKG-2024-020', 10, 'Mary Zulu', '741 Mongu Road, Mongu', '+260-**********', 'Rachel Green', '162 Redwood Drive, Libreville, Gabon', '+241-1-123-456', 'Standard', 'Ocean', 11.50, '54x44x34', 350.00, 82.00, 'Pending', 'Medium', 'Insurance required', '2024-02-02');

-- Insert Package details for shipments
INSERT INTO Package (ShipmentID, PackageNumber, Description, Weight, Dimensions, Value, Contents, IsFragile, RequiresSignature) VALUES
(1, 'PKG-2024-001-P1', 'Electronics and accessories', 2.50, '30x20x15', 500.00, 'Smartphone, charger, case', TRUE, TRUE),
(2, 'PKG-2024-002-P1', 'Documents and books', 5.00, '40x30x20', 200.00, 'Legal documents, reference books', FALSE, TRUE),
(3, 'PKG-2024-003-P1', 'Jewelry and valuables', 1.20, '25x15x10', 800.00, 'Gold jewelry, watches', TRUE, TRUE),
(4, 'PKG-2024-004-P1', 'Clothing and textiles', 8.50, '50x40x30', 150.00, 'Traditional clothing, fabrics', FALSE, FALSE),
(5, 'PKG-2024-005-P1', 'Medical supplies', 15.00, '60x50x40', 1200.00, 'Temperature-sensitive medications', TRUE, TRUE),
(6, 'PKG-2024-006-P1', 'Computer equipment', 3.75, '35x25x18', 600.00, 'Laptop, accessories', TRUE, TRUE),
(7, 'PKG-2024-007-P1', 'Art and crafts', 6.20, '45x35x25', 300.00, 'Handmade crafts, artwork', TRUE, FALSE),
(8, 'PKG-2024-008-P1', 'Business documents', 2.10, '28x18x12', 450.00, 'Contracts, certificates', FALSE, TRUE),
(9, 'PKG-2024-009-P1', 'Food products', 4.80, '38x28x22', 250.00, 'Specialty food items', FALSE, FALSE),
(10, 'PKG-2024-010-P1', 'Personal items', 1.90, '26x16x11', 700.00, 'Personal belongings', FALSE, TRUE);

-- Insert PickUp schedules
INSERT INTO PickUp (ShipmentID, ScheduledDate, ScheduledTime, ActualPickUpDate, PickUpAddress, ContactPerson, ContactPhone, Status, DriverID) VALUES
(1, '2024-01-14', '09:00:00', '2024-01-14 09:15:00', '123 Cairo Road, Lusaka', 'Michael Chisanga', '+260-**********', 'Completed', 9),
(2, '2024-01-15', '14:00:00', '2024-01-15 14:30:00', '456 Independence Avenue, Lusaka', 'Sarah Mubanga', '+260-**********', 'Completed', 10),
(3, '2024-01-16', '10:30:00', '2024-01-16 10:45:00', '789 Great East Road, Lusaka', 'Joseph Katongo', '+260-**********', 'Completed', 9),
(4, '2024-01-17', '11:00:00', '2024-01-17 11:20:00', '321 Kafue Road, Lusaka', 'Elizabeth Mwanza', '+260-977-444444', 'Completed', 10),
(5, '2024-01-18', '08:30:00', '2024-01-18 08:45:00', '654 Chiparamba Road, Kitwe', 'Patrick Lungu', '+260-**********', 'Completed', 9),
(6, '2024-01-19', '13:15:00', '2024-01-19 13:30:00', '987 Ndola Road, Kitwe', 'Grace Mulenga', '+260-977-666666', 'Completed', 10),
(7, '2024-01-20', '15:00:00', NULL, '147 Broadway, Ndola', 'Daniel Phiri', '+260-**********', 'Scheduled', 9),
(8, '2024-01-19', '12:00:00', '2024-01-19 12:15:00', '258 Kabwe Road, Kabwe', 'Ruth Banda', '+260-**********', 'Completed', 10),
(9, '2024-01-20', '16:30:00', '2024-01-20 16:45:00', '369 Livingstone Road, Livingstone', 'Samuel Tembo', '+260-**********', 'Completed', 9),
(10, '2024-01-18', '10:00:00', '2024-01-18 10:10:00', '741 Mongu Road, Mongu', 'Mary Zulu', '+260-**********', 'Completed', 10);

-- Insert 50+ Tracking Events
INSERT INTO TrackingEvent (ShipmentID, EventType, EventDescription, Location, EventDateTime, CreatedBy) VALUES
-- Shipment 1 (Delivered)
(1, 'Created', 'Shipment information received and processed', 'Lusaka, Zambia', '2024-01-14 08:00:00', 7),
(1, 'Picked Up', 'Package collected from sender', 'Lusaka, Zambia', '2024-01-14 09:15:00', 9),
(1, 'In Transit', 'Package departed from Lusaka facility', 'Lusaka International Airport', '2024-01-14 15:30:00', 9),
(1, 'In Transit', 'Package arrived at transit hub', 'OR Tambo International Airport', '2024-01-14 18:45:00', 10),
(1, 'Out for Delivery', 'Package out for delivery', 'Johannesburg, South Africa', '2024-01-15 08:00:00', 10),
(1, 'Delivered', 'Package delivered successfully', 'Johannesburg, South Africa', '2024-01-15 14:30:00', 10),

-- Shipment 2 (Delivered)
(2, 'Created', 'Shipment information received and processed', 'Lusaka, Zambia', '2024-01-15 09:00:00', 8),
(2, 'Picked Up', 'Package collected from sender', 'Lusaka, Zambia', '2024-01-15 14:30:00', 10),
(2, 'In Transit', 'Package in transit to destination', 'Lusaka-Nairobi Highway', '2024-01-16 06:00:00', 10),
(2, 'In Transit', 'Package arrived at border checkpoint', 'Nakonde Border Post', '2024-01-16 18:00:00', 9),
(2, 'In Transit', 'Package cleared customs', 'Nairobi Customs', '2024-01-17 10:00:00', 9),
(2, 'Out for Delivery', 'Package out for delivery', 'Nairobi, Kenya', '2024-01-18 07:30:00', 9),
(2, 'Delivered', 'Package delivered successfully', 'Nairobi, Kenya', '2024-01-18 16:20:00', 9),

-- Shipment 3 (In Transit)
(3, 'Created', 'Shipment information received and processed', 'Lusaka, Zambia', '2024-01-16 07:30:00', 7),
(3, 'Picked Up', 'Package collected from sender', 'Lusaka, Zambia', '2024-01-16 10:45:00', 9),
(3, 'In Transit', 'Package departed from Lusaka facility', 'Lusaka International Airport', '2024-01-16 16:00:00', 9),
(3, 'In Transit', 'Package in transit to destination', 'Harare International Airport', '2024-01-17 09:30:00', 10),

-- Shipment 4 (Out for Delivery)
(4, 'Created', 'Shipment information received and processed', 'Lusaka, Zambia', '2024-01-17 08:15:00', 8),
(4, 'Picked Up', 'Package collected from sender', 'Lusaka, Zambia', '2024-01-17 11:20:00', 10),
(4, 'In Transit', 'Package in transit to destination', 'Lusaka-Gaborone Highway', '2024-01-18 05:00:00', 10),
(4, 'In Transit', 'Package arrived at destination facility', 'Gaborone Distribution Center', '2024-01-21 14:00:00', 9),
(4, 'Out for Delivery', 'Package out for delivery', 'Gaborone, Botswana', '2024-01-22 08:00:00', 9),

-- Shipment 5 (Picked Up)
(5, 'Created', 'Shipment information received and processed', 'Kitwe, Zambia', '2024-01-18 07:00:00', 7),
(5, 'Picked Up', 'Package collected from sender', 'Kitwe, Zambia', '2024-01-18 08:45:00', 9),

-- Shipment 6 (In Transit)
(6, 'Created', 'Shipment information received and processed', 'Kitwe, Zambia', '2024-01-19 08:30:00', 8),
(6, 'Picked Up', 'Package collected from sender', 'Kitwe, Zambia', '2024-01-19 13:30:00', 10),
(6, 'In Transit', 'Package departed from Kitwe facility', 'Kitwe Distribution Center', '2024-01-19 18:00:00', 10),
(6, 'In Transit', 'Package in transit to destination', 'Windhoek via Air', '2024-01-20 12:00:00', 9),

-- Shipment 7 (Pending)
(7, 'Created', 'Shipment information received and processed', 'Ndola, Zambia', '2024-01-20 09:00:00', 7),

-- Shipment 8 (Picked Up)
(8, 'Created', 'Shipment information received and processed', 'Kabwe, Zambia', '2024-01-19 10:30:00', 8),
(8, 'Picked Up', 'Package collected from sender', 'Kabwe, Zambia', '2024-01-19 12:15:00', 10),

-- Shipment 9 (In Transit)
(9, 'Created', 'Shipment information received and processed', 'Livingstone, Zambia', '2024-01-20 11:00:00', 7),
(9, 'Picked Up', 'Package collected from sender', 'Livingstone, Zambia', '2024-01-20 16:45:00', 9),
(9, 'In Transit', 'Package in transit to destination', 'Livingstone-Kigali Route', '2024-01-21 06:00:00', 9),

-- Shipment 10 (Delivered)
(10, 'Created', 'Shipment information received and processed', 'Mongu, Zambia', '2024-01-18 08:00:00', 8),
(10, 'Picked Up', 'Package collected from sender', 'Mongu, Zambia', '2024-01-18 10:10:00', 10),
(10, 'In Transit', 'Package departed from Mongu facility', 'Mongu Distribution Center', '2024-01-18 15:00:00', 10),
(10, 'In Transit', 'Package arrived at transit hub', 'Entebbe International Airport', '2024-01-20 11:30:00', 9),
(10, 'Out for Delivery', 'Package out for delivery', 'Kampala, Uganda', '2024-01-21 07:00:00', 9),
(10, 'Delivered', 'Package delivered successfully', 'Kampala, Uganda', '2024-01-21 15:45:00', 9);

-- Insert Delivery records for completed shipments
INSERT INTO Delivery (ShipmentID, DeliveryDate, RecipientName, SignatureType, DeliveryNotes, DeliveryStatus, AttemptNumber, DeliveredBy) VALUES
(1, '2024-01-15 14:30:00', 'John Smith', 'Electronic', 'Package delivered to recipient at front door', 'Delivered', 1, 10),
(2, '2024-01-18 16:20:00', 'Jane Doe', 'Handwritten', 'Package delivered to office reception', 'Delivered', 1, 9),
(10, '2024-01-21 15:45:00', 'Rachel Green', 'Electronic', 'Package delivered to residential address', 'Delivered', 1, 9);

-- Insert Notifications for various events
INSERT INTO Notifications (UserID, Title, Message, Type, IsRead, CreatedAt) VALUES
-- Notifications for Customer 1
(11, 'Package Shipped', 'Your package PKG-2024-001 has been shipped and is on its way.', 'Shipment', FALSE, '2024-01-14 09:30:00'),
(11, 'Package Delivered', 'Your package PKG-2024-001 has been delivered successfully.', 'Delivery', TRUE, '2024-01-15 14:45:00'),

-- Notifications for Customer 2
(12, 'Package Shipped', 'Your package PKG-2024-002 has been shipped and is on its way.', 'Shipment', FALSE, '2024-01-15 15:00:00'),
(12, 'Package Delivered', 'Your package PKG-2024-002 has been delivered successfully.', 'Delivery', FALSE, '2024-01-18 16:30:00'),

-- Notifications for Customer 3
(13, 'Package Shipped', 'Your package PKG-2024-003 has been shipped and is on its way.', 'Shipment', FALSE, '2024-01-16 11:00:00'),
(13, 'Package In Transit', 'Your package PKG-2024-003 is currently in transit to Zimbabwe.', 'Tracking', FALSE, '2024-01-17 10:00:00'),

-- Notifications for Customer 4
(14, 'Package Shipped', 'Your package PKG-2024-004 has been shipped and is on its way.', 'Shipment', FALSE, '2024-01-17 11:30:00'),
(14, 'Out for Delivery', 'Your package PKG-2024-004 is out for delivery in Gaborone.', 'Delivery', FALSE, '2024-01-22 08:15:00'),

-- Notifications for Customer 5
(15, 'Package Shipped', 'Your package PKG-2024-005 has been shipped and is on its way.', 'Shipment', FALSE, '2024-01-18 09:00:00'),

-- System notifications for admins
(1, 'System Alert', 'Daily backup completed successfully.', 'System', TRUE, '2024-01-22 02:00:00'),
(2, 'New Customer Registration', 'New customer registered: Grace Mulenga', 'Customer', FALSE, '2024-01-19 14:00:00'),
(3, 'Delivery Issue', 'Delivery attempt failed for PKG-2024-007 - recipient not available', 'Delivery', FALSE, '2024-01-21 16:00:00'),

-- Insert Analytics data
INSERT INTO Analytics (UserID, Action, Category, Details, IPAddress, UserAgent, CreatedAt) VALUES
(11, 'TRACK_PACKAGE', 'Tracking', '{"tracking_number":"PKG-2024-001","status":"Delivered"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '2024-01-15 15:00:00'),
(12, 'TRACK_PACKAGE', 'Tracking', '{"tracking_number":"PKG-2024-002","status":"Delivered"}', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', '2024-01-18 17:00:00'),
(13, 'TRACK_PACKAGE', 'Tracking', '{"tracking_number":"PKG-2024-003","status":"In Transit"}', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1)', '2024-01-17 11:00:00'),
(1, 'LOGIN', 'Authentication', '{"role":"SuperAdmin","2fa_used":true}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '2024-01-22 08:00:00'),
(2, 'LOGIN', 'Authentication', '{"role":"SubAdmin","2fa_used":false}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '2024-01-22 08:30:00'),
(7, 'CREATE_SHIPMENT', 'Shipment', '{"tracking_number":"PKG-2024-020","customer_id":10}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '2024-02-02 10:00:00'),
(NULL, 'TRACK_PACKAGE', 'Tracking', '{"tracking_number":"PKG-2024-001","status":"Delivered","ip_address":"***********"}', '***********', 'Mozilla/5.0 (Android 11; Mobile)', '2024-01-16 12:00:00'),
(NULL, 'TRACK_PACKAGE', 'Tracking', '{"tracking_number":"PKG-2024-002","status":"Delivered","ip_address":"***********"}', '***********', 'Mozilla/5.0 (iPad; CPU OS 14_7_1)', '2024-01-19 09:00:00'),
(8, 'UPDATE_SHIPMENT', 'Shipment', '{"tracking_number":"PKG-2024-005","status_changed":"Pending to Picked Up"}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', '2024-01-18 09:00:00'),
(9, 'DELIVERY_ATTEMPT', 'Delivery', '{"tracking_number":"PKG-2024-001","attempt_number":1,"status":"Delivered"}', '************', 'Mozilla/5.0 (Android 10; Mobile)', '2024-01-15 14:30:00');

-- Insert Error Log entries
INSERT INTO ErrorLog (UserID, ErrorType, ErrorMessage, StackTrace, RequestData, CreatedAt) VALUES
(NULL, 'TRACKING_ERROR', 'Invalid tracking number format', 'at track.php:45', '{"tracking_number":"INVALID-123"}', '2024-01-20 10:30:00'),
(12, 'LOGIN_FAILED', 'Invalid password for user: <EMAIL>', 'at login.php:78', '{"username":"<EMAIL>","ip":"*************"}', '2024-01-18 09:15:00'),
(NULL, 'DATABASE_ERROR', 'Connection timeout to database server', 'at database.php:23', '{"query":"SELECT * FROM Shipment"}', '2024-01-21 14:45:00'),
(7, 'VALIDATION_ERROR', 'Invalid shipment weight: must be positive number', 'at shipment.php:156', '{"weight":"-5.0","tracking_number":"PKG-2024-021"}', '2024-01-22 11:20:00'),
(NULL, 'API_ERROR', 'Rate limit exceeded for IP address', 'at api_middleware.php:34', '{"ip":"*************","endpoint":"/api/tracking/track.php"}', '2024-01-22 15:30:00');
