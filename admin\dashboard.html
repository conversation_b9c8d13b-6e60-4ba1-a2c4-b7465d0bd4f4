<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Admin Dashboard - ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="ZamSend admin dashboard for managing users, shipments, customers, and system analytics." />
	<meta name="keywords" content="admin dashboard, courier management, user management, analytics, ZamSend" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="../css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="../css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="../css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="../css/style.css">
	<!-- Chart.js -->
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	
	<!-- Custom admin dashboard styles -->
	<style>
		.admin-header {
			background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
			color: white;
			padding: 20px 0;
		}
		
		.admin-sidebar {
			background: #343a40;
			min-height: calc(100vh - 80px);
			padding: 0;
		}
		
		.admin-sidebar .nav-link {
			color: #adb5bd;
			padding: 15px 20px;
			border-bottom: 1px solid #495057;
			display: flex;
			align-items: center;
			gap: 10px;
		}
		
		.admin-sidebar .nav-link:hover,
		.admin-sidebar .nav-link.active {
			background: #495057;
			color: white;
		}
		
		.admin-content {
			padding: 30px;
			background: #f8f9fa;
			min-height: calc(100vh - 80px);
		}
		
		.admin-card {
			background: white;
			border-radius: 15px;
			box-shadow: 0 5px 20px rgba(0,0,0,0.1);
			padding: 25px;
			margin-bottom: 30px;
		}
		
		.stat-card {
			text-align: center;
			padding: 30px 20px;
			border-left: 4px solid;
		}
		
		.stat-card.users { border-left-color: #007bff; }
		.stat-card.customers { border-left-color: #28a745; }
		.stat-card.shipments { border-left-color: #ffc107; }
		.stat-card.revenue { border-left-color: #dc3545; }
		
		.stat-card .stat-number {
			font-size: 2.5rem;
			font-weight: 700;
			margin-bottom: 10px;
		}
		
		.stat-card.users .stat-number { color: #007bff; }
		.stat-card.customers .stat-number { color: #28a745; }
		.stat-card.shipments .stat-number { color: #ffc107; }
		.stat-card.revenue .stat-number { color: #dc3545; }
		
		.stat-card .stat-label {
			color: #6c757d;
			font-size: 14px;
			text-transform: uppercase;
			letter-spacing: 1px;
		}
		
		.chart-container {
			position: relative;
			height: 300px;
			margin: 20px 0;
		}
		
		.table-responsive {
			border-radius: 10px;
			overflow: hidden;
		}
		
		.status-badge {
			padding: 5px 12px;
			border-radius: 15px;
			font-size: 12px;
			font-weight: 600;
		}
		
		.status-active { background: #d4edda; color: #155724; }
		.status-inactive { background: #f8d7da; color: #721c24; }
		.status-pending { background: #fff3cd; color: #856404; }
		.status-delivered { background: #d1ecf1; color: #0c5460; }
		
		.quick-actions .btn {
			margin: 5px;
			border-radius: 25px;
			padding: 10px 25px;
		}
		
		.user-info {
			display: flex;
			align-items: center;
			gap: 15px;
		}
		
		.user-avatar {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			background: rgba(255,255,255,0.2);
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 16px;
		}
		
		.alert-item {
			padding: 15px;
			border-left: 4px solid #dc3545;
			margin-bottom: 10px;
			background: #f8f9fa;
		}
		
		.alert-item.warning { border-left-color: #ffc107; }
		.alert-item.info { border-left-color: #17a2b8; }
		.alert-item.success { border-left-color: #28a745; }
	</style>

	<!-- Modernizr JS -->
	<script src="../js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<!-- Admin Header -->
	<div class="admin-header">
		<div class="container-fluid">
			<div class="row">
				<div class="col-md-6">
					<h2><i class="icon-shield"></i> ZamSend Admin Dashboard</h2>
				</div>
				<div class="col-md-6 text-right">
					<div class="user-info">
						<div class="user-avatar">
							<i class="icon-user"></i>
						</div>
						<div>
							<div id="adminName">Super Admin</div>
							<small>System Administrator</small>
						</div>
						<div class="dropdown">
							<button class="btn btn-link text-white dropdown-toggle" type="button" data-toggle="dropdown">
								<i class="icon-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-right">
								<li><a href="profile.html">Profile</a></li>
								<li><a href="system-settings.html">System Settings</a></li>
								<li class="divider"></li>
								<li><a href="#" id="logoutBtn">Logout</a></li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="container-fluid">
		<div class="row">
			<!-- Admin Sidebar -->
			<div class="col-md-2 admin-sidebar">
				<nav class="nav flex-column">
					<a class="nav-link active" href="#dashboard" data-toggle="pill">
						<i class="icon-home"></i> Dashboard
					</a>
					<a class="nav-link" href="#users" data-toggle="pill">
						<i class="icon-users"></i> User Management
					</a>
					<a class="nav-link" href="#customers" data-toggle="pill">
						<i class="icon-user"></i> Customers
					</a>
					<a class="nav-link" href="#shipments" data-toggle="pill">
						<i class="icon-truck"></i> Shipments
					</a>
					<a class="nav-link" href="#analytics" data-toggle="pill">
						<i class="icon-bar-chart"></i> Analytics
					</a>
					<a class="nav-link" href="#reports" data-toggle="pill">
						<i class="icon-file-text"></i> Reports
					</a>
					<a class="nav-link" href="#system" data-toggle="pill">
						<i class="icon-cog"></i> System
					</a>
				</nav>
			</div>

			<!-- Admin Content -->
			<div class="col-md-10 admin-content">
				<div class="tab-content">
					<!-- Dashboard Tab -->
					<div class="tab-pane fade in active" id="dashboard">
						<h3>System Overview</h3>
						
						<!-- Statistics Cards -->
						<div class="row">
							<div class="col-md-3">
								<div class="admin-card stat-card users">
									<div class="stat-number" id="totalUsers">0</div>
									<div class="stat-label">Total Users</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="admin-card stat-card customers">
									<div class="stat-number" id="totalCustomers">0</div>
									<div class="stat-label">Total Customers</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="admin-card stat-card shipments">
									<div class="stat-number" id="totalShipments">0</div>
									<div class="stat-label">Total Shipments</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="admin-card stat-card revenue">
									<div class="stat-number" id="totalRevenue">$0</div>
									<div class="stat-label">Total Revenue</div>
								</div>
							</div>
						</div>

						<!-- Charts Row -->
						<div class="row">
							<div class="col-md-8">
								<div class="admin-card">
									<h4>Shipments Overview</h4>
									<div class="chart-container">
										<canvas id="shipmentsChart"></canvas>
									</div>
								</div>
							</div>
							<div class="col-md-4">
								<div class="admin-card">
									<h4>Status Distribution</h4>
									<div class="chart-container">
										<canvas id="statusChart"></canvas>
									</div>
								</div>
							</div>
						</div>

						<!-- Recent Activity and Alerts -->
						<div class="row">
							<div class="col-md-6">
								<div class="admin-card">
									<h4>Recent Activity</h4>
									<div id="recentActivity">
										<div class="alert-item info">
											<strong>New shipment created:</strong> PKG-2024-001234
											<small class="text-muted d-block">2 minutes ago</small>
										</div>
										<div class="alert-item success">
											<strong>Package delivered:</strong> PKG-2024-001230
											<small class="text-muted d-block">15 minutes ago</small>
										</div>
										<div class="alert-item warning">
											<strong>Delivery delayed:</strong> PKG-2024-001225
											<small class="text-muted d-block">1 hour ago</small>
										</div>
									</div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="admin-card">
									<h4>System Alerts</h4>
									<div id="systemAlerts">
										<div class="alert-item">
											<strong>High server load detected</strong>
											<small class="text-muted d-block">System performance may be affected</small>
										</div>
										<div class="alert-item warning">
											<strong>Low disk space warning</strong>
											<small class="text-muted d-block">Database server at 85% capacity</small>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Other tabs content placeholders -->
					<div class="tab-pane fade" id="users">
						<div class="admin-card">
							<h3>User Management</h3>
							<p>User management interface will be loaded here.</p>
						</div>
					</div>

					<div class="tab-pane fade" id="customers">
						<div class="admin-card">
							<h3>Customer Management</h3>
							<p>Customer management interface will be loaded here.</p>
						</div>
					</div>

					<div class="tab-pane fade" id="shipments">
						<div class="admin-card">
							<h3>Shipment Management</h3>
							<p>Shipment management interface will be loaded here.</p>
						</div>
					</div>

					<div class="tab-pane fade" id="analytics">
						<div class="admin-card">
							<h3>Analytics & Insights</h3>
							<p>Analytics dashboard will be loaded here.</p>
						</div>
					</div>

					<div class="tab-pane fade" id="reports">
						<div class="admin-card">
							<h3>Reports</h3>
							<p>Reports interface will be loaded here.</p>
						</div>
					</div>

					<div class="tab-pane fade" id="system">
						<div class="admin-card">
							<h3>System Settings</h3>
							<p>System configuration interface will be loaded here.</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	</div>

	<!-- jQuery -->
	<script src="../js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="../js/bootstrap.min.js"></script>
	
	<script>
		$(document).ready(function() {
			// Check admin session
			checkAdminSession();
			
			// Load dashboard data
			loadDashboardData();
			
			// Initialize charts
			initializeCharts();
			
			// Logout functionality
			$('#logoutBtn').on('click', function(e) {
				e.preventDefault();
				logout();
			});
		});
		
		function checkAdminSession() {
			$.ajax({
				url: '../api/auth/check-session.php',
				method: 'GET',
				success: function(response) {
					if (response.success) {
						const user = response.data.user;
						if (user.role !== 'SuperAdmin') {
							alert('Access denied. Super Admin privileges required.');
							window.location.href = '../login.html';
							return;
						}
						$('#adminName').text(user.first_name + ' ' + user.last_name);
					}
				},
				error: function() {
					window.location.href = '../login.html';
				}
			});
		}
		
		function loadDashboardData() {
			// Load statistics (these would come from API endpoints)
			$('#totalUsers').text('10');
			$('#totalCustomers').text('150');
			$('#totalShipments').text('1,250');
			$('#totalRevenue').text('$45,750');
		}
		
		function initializeCharts() {
			// Shipments Chart
			const shipmentsCtx = document.getElementById('shipmentsChart').getContext('2d');
			new Chart(shipmentsCtx, {
				type: 'line',
				data: {
					labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
					datasets: [{
						label: 'Shipments',
						data: [65, 78, 90, 81, 95, 105],
						borderColor: '#007bff',
						backgroundColor: 'rgba(0, 123, 255, 0.1)',
						tension: 0.4
					}]
				},
				options: {
					responsive: true,
					maintainAspectRatio: false
				}
			});
			
			// Status Chart
			const statusCtx = document.getElementById('statusChart').getContext('2d');
			new Chart(statusCtx, {
				type: 'doughnut',
				data: {
					labels: ['Delivered', 'In Transit', 'Pending', 'Cancelled'],
					datasets: [{
						data: [45, 25, 20, 10],
						backgroundColor: ['#28a745', '#007bff', '#ffc107', '#dc3545']
					}]
				},
				options: {
					responsive: true,
					maintainAspectRatio: false
				}
			});
		}
		
		function logout() {
			$.ajax({
				url: '../api/auth/logout.php',
				method: 'POST',
				success: function() {
					window.location.href = '../login.html';
				},
				error: function() {
					window.location.href = '../login.html';
				}
			});
		}
	</script>

	</body>
</html>
