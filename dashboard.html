<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Dashboard - ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="ZamSend courier services dashboard for managing shipments and tracking packages." />
	<meta name="keywords" content="dashboard, courier services, shipment management, package tracking, ZamSend" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">
	
	<!-- Custom dashboard styles -->
	<style>
		.dashboard-header {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 30px 0;
		}
		
		.dashboard-nav {
			background: #f8f9fa;
			border-bottom: 1px solid #dee2e6;
			padding: 15px 0;
		}
		
		.dashboard-nav .nav-pills .nav-link {
			color: #495057;
			border-radius: 20px;
			margin: 0 5px;
		}
		
		.dashboard-nav .nav-pills .nav-link.active {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		}
		
		.dashboard-card {
			background: white;
			border-radius: 15px;
			box-shadow: 0 5px 20px rgba(0,0,0,0.1);
			padding: 25px;
			margin-bottom: 30px;
			transition: transform 0.3s ease;
		}
		
		.dashboard-card:hover {
			transform: translateY(-5px);
		}
		
		.stat-card {
			text-align: center;
			padding: 30px 20px;
		}
		
		.stat-card .stat-number {
			font-size: 2.5rem;
			font-weight: 700;
			color: #667eea;
			margin-bottom: 10px;
		}
		
		.stat-card .stat-label {
			color: #6c757d;
			font-size: 14px;
			text-transform: uppercase;
			letter-spacing: 1px;
		}
		
		.recent-shipments table {
			margin-bottom: 0;
		}
		
		.status-badge {
			padding: 5px 12px;
			border-radius: 15px;
			font-size: 12px;
			font-weight: 600;
		}
		
		.status-pending { background: #fff3cd; color: #856404; }
		.status-picked-up { background: #d4edda; color: #155724; }
		.status-in-transit { background: #cce7ff; color: #004085; }
		.status-out-for-delivery { background: #f8d7da; color: #721c24; }
		.status-delivered { background: #d1ecf1; color: #0c5460; }
		
		.quick-actions .btn {
			margin: 5px;
			border-radius: 25px;
			padding: 10px 25px;
		}
		
		.user-info {
			display: flex;
			align-items: center;
			gap: 15px;
		}
		
		.user-avatar {
			width: 50px;
			height: 50px;
			border-radius: 50%;
			background: rgba(255,255,255,0.2);
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 20px;
		}
		
		.notification-badge {
			position: relative;
		}
		
		.notification-badge .badge {
			position: absolute;
			top: -8px;
			right: -8px;
			background: #dc3545;
			color: white;
			border-radius: 50%;
			width: 20px;
			height: 20px;
			font-size: 10px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<!-- Dashboard Header -->
	<div class="dashboard-header">
		<div class="container">
			<div class="row">
				<div class="col-md-6">
					<h1>Welcome to ZamSend Dashboard</h1>
					<p id="welcomeMessage">Manage your shipments and track packages</p>
				</div>
				<div class="col-md-6 text-right">
					<div class="user-info">
						<div class="notification-badge">
							<i class="icon-bell" style="font-size: 24px;"></i>
							<span class="badge" id="notificationCount">0</span>
						</div>
						<div class="user-avatar">
							<i class="icon-user"></i>
						</div>
						<div>
							<div id="userName">Loading...</div>
							<small id="userRole">User</small>
						</div>
						<div class="dropdown">
							<button class="btn btn-link text-white dropdown-toggle" type="button" data-toggle="dropdown">
								<i class="icon-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-right">
								<li><a href="profile.html">Profile</a></li>
								<li><a href="settings.html">Settings</a></li>
								<li class="divider"></li>
								<li><a href="#" id="logoutBtn">Logout</a></li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Dashboard Navigation -->
	<div class="dashboard-nav">
		<div class="container">
			<ul class="nav nav-pills">
				<li class="nav-item">
					<a class="nav-link active" href="#overview" data-toggle="pill">Overview</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="#shipments" data-toggle="pill">My Shipments</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="#tracking" data-toggle="pill">Track Package</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="#reports" data-toggle="pill">Reports</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="ship-now.html">Ship Now</a>
				</li>
			</ul>
		</div>
	</div>

	<!-- Dashboard Content -->
	<div class="container" style="margin-top: 30px;">
		<div class="tab-content">
			<!-- Overview Tab -->
			<div class="tab-pane fade in active" id="overview">
				<!-- Statistics Cards -->
				<div class="row">
					<div class="col-md-3">
						<div class="dashboard-card stat-card">
							<div class="stat-number" id="totalShipments">0</div>
							<div class="stat-label">Total Shipments</div>
						</div>
					</div>
					<div class="col-md-3">
						<div class="dashboard-card stat-card">
							<div class="stat-number" id="activeShipments">0</div>
							<div class="stat-label">Active Shipments</div>
						</div>
					</div>
					<div class="col-md-3">
						<div class="dashboard-card stat-card">
							<div class="stat-number" id="deliveredShipments">0</div>
							<div class="stat-label">Delivered</div>
						</div>
					</div>
					<div class="col-md-3">
						<div class="dashboard-card stat-card">
							<div class="stat-number" id="pendingShipments">0</div>
							<div class="stat-label">Pending</div>
						</div>
					</div>
				</div>

				<!-- Quick Actions -->
				<div class="row">
					<div class="col-md-12">
						<div class="dashboard-card">
							<h4>Quick Actions</h4>
							<div class="quick-actions">
								<a href="ship-now.html" class="btn btn-primary">
									<i class="icon-plus"></i> Create New Shipment
								</a>
								<a href="tracking.html" class="btn btn-info">
									<i class="icon-search"></i> Track Package
								</a>
								<a href="calculator.html" class="btn btn-success">
									<i class="icon-calculator"></i> Calculate Shipping Cost
								</a>
								<a href="pickup.html" class="btn btn-warning">
									<i class="icon-truck"></i> Schedule Pickup
								</a>
							</div>
						</div>
					</div>
				</div>

				<!-- Recent Shipments -->
				<div class="row">
					<div class="col-md-12">
						<div class="dashboard-card recent-shipments">
							<h4>Recent Shipments</h4>
							<div class="table-responsive">
								<table class="table table-hover">
									<thead>
										<tr>
											<th>Tracking Number</th>
											<th>Recipient</th>
											<th>Destination</th>
											<th>Status</th>
											<th>Date</th>
											<th>Actions</th>
										</tr>
									</thead>
									<tbody id="recentShipmentsTable">
										<tr>
											<td colspan="6" class="text-center">Loading shipments...</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Other tabs content will be loaded dynamically -->
			<div class="tab-pane fade" id="shipments">
				<div class="dashboard-card">
					<h4>All My Shipments</h4>
					<p>Detailed shipment management interface will be loaded here.</p>
				</div>
			</div>

			<div class="tab-pane fade" id="tracking">
				<div class="dashboard-card">
					<h4>Package Tracking</h4>
					<p>Enhanced tracking interface will be loaded here.</p>
				</div>
			</div>

			<div class="tab-pane fade" id="reports">
				<div class="dashboard-card">
					<h4>Reports & Analytics</h4>
					<p>Reporting dashboard will be loaded here.</p>
				</div>
			</div>
		</div>
	</div>

	<footer id="fh5co-footer" role="contentinfo" style="margin-top: 100px;">
		<div class="container">
			<div class="row copyright">
				<div class="col-md-12 text-center">
					<p>
						<small class="block">&copy; 2024 ZamSend Courier Services. All Rights Reserved.</small> 
					</p>
				</div>
			</div>
		</div>
	</footer>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	
	<script>
		$(document).ready(function() {
			// Check if user is logged in
			checkSession();
			
			// Load dashboard data
			loadDashboardData();
			
			// Logout functionality
			$('#logoutBtn').on('click', function(e) {
				e.preventDefault();
				logout();
			});
		});
		
		function checkSession() {
			$.ajax({
				url: 'api/auth/check-session.php',
				method: 'GET',
				success: function(response) {
					if (response.success) {
						const user = response.data.user;
						$('#userName').text(user.first_name + ' ' + user.last_name);
						$('#userRole').text(user.role);
						$('#welcomeMessage').text(`Welcome back, ${user.first_name}!`);
						$('#notificationCount').text(response.data.unread_notifications);
					}
				},
				error: function() {
					// Redirect to login if session is invalid
					window.location.href = 'login.html';
				}
			});
		}
		
		function loadDashboardData() {
			// Load statistics and recent shipments
			// This would typically call various API endpoints
			
			// For demo purposes, set some sample data
			$('#totalShipments').text('24');
			$('#activeShipments').text('8');
			$('#deliveredShipments').text('15');
			$('#pendingShipments').text('1');
			
			// Load recent shipments (this would be from an API)
			const sampleShipments = [
				{
					tracking: 'PKG-2024-001',
					recipient: 'John Smith',
					destination: 'Johannesburg, SA',
					status: 'Delivered',
					date: '2024-01-15'
				},
				{
					tracking: 'PKG-2024-002',
					recipient: 'Jane Doe',
					destination: 'Nairobi, Kenya',
					status: 'In Transit',
					date: '2024-01-18'
				}
			];
			
			let tableHtml = '';
			sampleShipments.forEach(function(shipment) {
				const statusClass = 'status-' + shipment.status.toLowerCase().replace(/\s+/g, '-');
				tableHtml += `
					<tr>
						<td><strong>${shipment.tracking}</strong></td>
						<td>${shipment.recipient}</td>
						<td>${shipment.destination}</td>
						<td><span class="status-badge ${statusClass}">${shipment.status}</span></td>
						<td>${shipment.date}</td>
						<td>
							<a href="tracking.html?tracking=${shipment.tracking}" class="btn btn-sm btn-info">Track</a>
							<a href="shipment-details.html?id=${shipment.tracking}" class="btn btn-sm btn-default">Details</a>
						</td>
					</tr>
				`;
			});
			
			$('#recentShipmentsTable').html(tableHtml);
		}
		
		function logout() {
			$.ajax({
				url: 'api/auth/logout.php',
				method: 'POST',
				success: function() {
					window.location.href = 'login.html';
				},
				error: function() {
					// Force logout anyway
					window.location.href = 'login.html';
				}
			});
		}
	</script>

	</body>
</html>
