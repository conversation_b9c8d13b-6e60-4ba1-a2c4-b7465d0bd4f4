<!DOCTYPE HTML>
<html>
	<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Ship Now - ZamSend Courier Services</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="Create a new shipment with ZamSend courier services. Fast, secure international shipping with real-time tracking." />
	<meta name="keywords" content="ship now, create shipment, international shipping, courier booking, ZamSend" />
	<meta name="author" content="ZamSend Courier Services" />

	<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,700,800" rel="stylesheet">
	
	<!-- Animate.css -->
	<link rel="stylesheet" href="css/animate.css">
	<!-- Icomoon Icon Fonts-->
	<link rel="stylesheet" href="css/icomoon.css">
	<!-- Bootstrap  -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<!-- Theme style  -->
	<link rel="stylesheet" href="css/style.css">

	<!-- Custom ship-now styles -->
	<style>
		.ship-hero {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 80px 0;
		}
		
		.ship-form {
			background: white;
			border-radius: 15px;
			padding: 40px;
			box-shadow: 0 10px 30px rgba(0,0,0,0.1);
			margin-top: -50px;
			position: relative;
			z-index: 10;
		}
		
		.form-section {
			margin-bottom: 40px;
			padding: 30px;
			border: 1px solid #e9ecef;
			border-radius: 10px;
		}
		
		.form-section h4 {
			color: #667eea;
			margin-bottom: 20px;
			padding-bottom: 10px;
			border-bottom: 2px solid #f8f9fa;
		}
		
		.form-control {
			height: 45px;
			border-radius: 8px;
			border: 2px solid #e1e5e9;
			padding: 0 15px;
			margin-bottom: 15px;
		}
		
		.form-control:focus {
			border-color: #667eea;
			box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
		}
		
		.btn-ship {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			border-radius: 25px;
			color: white;
			font-size: 18px;
			font-weight: 600;
			padding: 15px 40px;
			width: 100%;
		}
		
		.btn-ship:hover {
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
		}
		
		.cost-calculator {
			background: #f8f9fa;
			padding: 20px;
			border-radius: 10px;
			margin-top: 20px;
		}
		
		.cost-display {
			font-size: 24px;
			font-weight: 700;
			color: #667eea;
			text-align: center;
		}
		
		.service-selector {
			display: flex;
			gap: 15px;
			margin-bottom: 20px;
		}
		
		.service-option {
			flex: 1;
			padding: 15px;
			border: 2px solid #e9ecef;
			border-radius: 10px;
			text-align: center;
			cursor: pointer;
			transition: all 0.3s ease;
		}
		
		.service-option:hover,
		.service-option.selected {
			border-color: #667eea;
			background: rgba(102, 126, 234, 0.1);
		}
		
		.service-option h5 {
			margin-bottom: 5px;
			color: #333;
		}
		
		.service-option .price {
			color: #667eea;
			font-weight: 600;
		}
		
		.alert {
			border-radius: 10px;
			margin-bottom: 20px;
		}
		
		.loading {
			display: none;
		}
		
		.loading .spinner-border {
			width: 20px;
			height: 20px;
		}
	</style>

	<!-- Modernizr JS -->
	<script src="js/modernizr-2.6.2.min.js"></script>
	</head>
	<body>
		
	<div id="page">
	<nav class="fh5co-nav" role="navigation">
		<div class="top">
			<div class="container">
				<div class="row">
					<div class="col-xs-12 text-right">
						<p class="num">Call: +260-211-123456 | Email: <EMAIL></p>
						<ul class="fh5co-social">
							<li><a href="#" title="Facebook"><i class="icon-facebook"></i></a></li>
							<li><a href="#" title="Twitter"><i class="icon-twitter"></i></a></li>
							<li><a href="#" title="LinkedIn"><i class="icon-linkedin"></i></a></li>
							<li><a href="#" title="WhatsApp"><i class="icon-phone"></i></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<div class="top-menu">
			<div class="container">
				<div class="row">
					<div class="col-xs-2">
						<div id="fh5co-logo"><a href="index.html">ZamSend<span>.</span></a></div>
					</div>
					<div class="col-xs-10 text-right menu-1">
						<ul>
							<li><a href="index.html">Home</a></li>
							<li><a href="services.html">Services</a></li>
							<li><a href="tracking.html">Track Package</a></li>
							<li class="has-dropdown">
								<a href="pricing.html">Shipping</a>
								<ul class="dropdown">
									<li><a href="pricing.html">Rates & Pricing</a></li>
									<li><a href="calculator.html">Shipping Calculator</a></li>
									<li><a href="zones.html">Delivery Zones</a></li>
									<li><a href="restrictions.html">Shipping Restrictions</a></li>
								</ul>
							</li>
							<li><a href="about.html">About</a></li>
							<li><a href="contact.html">Contact</a></li>
							<li class="btn-cta"><a href="login.html"><span>Login</span></a></li>
							<li class="btn-cta"><a href="register.html"><span>Sign Up</span></a></li>
							<li class="btn-cta active"><a href="ship-now.html"><span>Ship Now</span></a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>

	<div class="ship-hero">
		<div class="container">
			<div class="row">
				<div class="col-md-8 col-md-offset-2 text-center">
					<h1>Ship Your Package</h1>
					<p>Fast, secure international shipping with real-time tracking</p>
				</div>
			</div>
		</div>
	</div>

	<div class="container">
		<div class="row">
			<div class="col-md-10 col-md-offset-1">
				<div class="ship-form">
					<div id="alert-container"></div>
					
					<form id="shipmentForm">
						<!-- Service Selection -->
						<div class="form-section">
							<h4><i class="icon-truck"></i> Choose Service</h4>
							<div class="service-selector">
								<div class="service-option" data-service="economy">
									<h5>Economy</h5>
									<div class="price">$10/kg</div>
									<small>10-15 days</small>
								</div>
								<div class="service-option selected" data-service="standard">
									<h5>Standard</h5>
									<div class="price">$15/kg</div>
									<small>5-10 days</small>
								</div>
								<div class="service-option" data-service="express">
									<h5>Express</h5>
									<div class="price">$25/kg</div>
									<small>2-5 days</small>
								</div>
								<div class="service-option" data-service="special">
									<h5>Special</h5>
									<div class="price">$50/kg</div>
									<small>1-3 days</small>
								</div>
							</div>
							<input type="hidden" id="selectedService" name="shipment_type" value="Standard">
						</div>

						<!-- Sender Information -->
						<div class="form-section">
							<h4><i class="icon-user"></i> Sender Information</h4>
							<div class="row">
								<div class="col-md-6">
									<input type="text" class="form-control" name="sender_name" placeholder="Full Name" required>
								</div>
								<div class="col-md-6">
									<input type="tel" class="form-control" name="sender_phone" placeholder="Phone Number" required>
								</div>
							</div>
							<div class="row">
								<div class="col-md-12">
									<textarea class="form-control" name="sender_address" rows="3" placeholder="Complete Address" required></textarea>
								</div>
							</div>
						</div>

						<!-- Recipient Information -->
						<div class="form-section">
							<h4><i class="icon-location"></i> Recipient Information</h4>
							<div class="row">
								<div class="col-md-6">
									<input type="text" class="form-control" name="recipient_name" placeholder="Full Name" required>
								</div>
								<div class="col-md-6">
									<input type="tel" class="form-control" name="recipient_phone" placeholder="Phone Number" required>
								</div>
							</div>
							<div class="row">
								<div class="col-md-12">
									<textarea class="form-control" name="recipient_address" rows="3" placeholder="Complete Address" required></textarea>
								</div>
							</div>
						</div>

						<!-- Package Information -->
						<div class="form-section">
							<h4><i class="icon-archive"></i> Package Information</h4>
							<div class="row">
								<div class="col-md-4">
									<input type="number" class="form-control" name="weight" placeholder="Weight (kg)" step="0.1" min="0.1" required>
								</div>
								<div class="col-md-4">
									<input type="text" class="form-control" name="dimensions" placeholder="Dimensions (LxWxH cm)">
								</div>
								<div class="col-md-4">
									<input type="number" class="form-control" name="declared_value" placeholder="Declared Value ($)" step="0.01" min="0">
								</div>
							</div>
							<div class="row">
								<div class="col-md-6">
									<input type="text" class="form-control" name="package_description" placeholder="Package Description" required>
								</div>
								<div class="col-md-6">
									<select class="form-control" name="delivery_mode" required>
										<option value="">Select Delivery Mode</option>
										<option value="Air">Air Transport</option>
										<option value="Road">Road Transport</option>
										<option value="Rail">Rail Transport</option>
										<option value="Ocean">Ocean Transport</option>
									</select>
								</div>
							</div>
							<div class="row">
								<div class="col-md-12">
									<textarea class="form-control" name="special_instructions" rows="2" placeholder="Special Instructions (Optional)"></textarea>
								</div>
							</div>
							<div class="row">
								<div class="col-md-6">
									<label class="checkbox-inline">
										<input type="checkbox" name="is_fragile"> Fragile Item
									</label>
								</div>
								<div class="col-md-6">
									<label class="checkbox-inline">
										<input type="checkbox" name="requires_signature" checked> Signature Required
									</label>
								</div>
							</div>
						</div>

						<!-- Cost Calculator -->
						<div class="cost-calculator">
							<h5>Estimated Shipping Cost</h5>
							<div class="cost-display" id="estimatedCost">$0.00</div>
							<small class="text-muted">Final cost may vary based on actual package dimensions and destination</small>
						</div>

						<!-- Submit Button -->
						<div style="margin-top: 30px;">
							<button type="submit" class="btn btn-ship">
								<span class="ship-text">Create Shipment</span>
								<span class="loading">
									<span class="spinner-border spinner-border-sm" role="status"></span>
									Creating shipment...
								</span>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>

	<footer id="fh5co-footer" role="contentinfo" style="margin-top: 100px;">
		<div class="container">
			<div class="row copyright">
				<div class="col-md-12 text-center">
					<p>
						<small class="block">&copy; 2024 ZamSend Courier Services. All Rights Reserved.</small> 
					</p>
				</div>
			</div>
		</div>
	</footer>
	</div>

	<!-- jQuery -->
	<script src="js/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="js/bootstrap.min.js"></script>
	<!-- Main -->
	<script src="js/main.js"></script>
	
	<script>
		$(document).ready(function() {
			// Service selection
			$('.service-option').on('click', function() {
				$('.service-option').removeClass('selected');
				$(this).addClass('selected');
				
				const service = $(this).data('service');
				const serviceMap = {
					'economy': 'Economy',
					'standard': 'Standard',
					'express': 'Express',
					'special': 'Special Handling'
				};
				
				$('#selectedService').val(serviceMap[service]);
				calculateCost();
			});
			
			// Calculate cost on weight change
			$('input[name="weight"]').on('input', calculateCost);
			
			// Form submission
			$('#shipmentForm').on('submit', function(e) {
				e.preventDefault();
				
				// Check if user is logged in first
				$.ajax({
					url: 'api/auth/check-session.php',
					method: 'GET',
					success: function(response) {
						if (response.success) {
							createShipment();
						} else {
							showAlert('Please log in to create a shipment.', 'warning');
							setTimeout(function() {
								window.location.href = 'login.html';
							}, 2000);
						}
					},
					error: function() {
						showAlert('Please log in to create a shipment.', 'warning');
						setTimeout(function() {
							window.location.href = 'login.html';
						}, 2000);
					}
				});
			});
		});
		
		function calculateCost() {
			const weight = parseFloat($('input[name="weight"]').val()) || 0;
			const service = $('.service-option.selected').data('service');
			
			const rates = {
				'economy': 10,
				'standard': 15,
				'express': 25,
				'special': 50
			};
			
			const cost = weight * rates[service];
			$('#estimatedCost').text('$' + cost.toFixed(2));
		}
		
		function createShipment() {
			const formData = new FormData($('#shipmentForm')[0]);
			const data = {};
			
			// Convert FormData to object
			for (let [key, value] of formData.entries()) {
				data[key] = value;
			}
			
			// Add customer ID (this would normally come from session)
			data.customer_id = 11; // Demo customer ID
			
			// Show loading state
			$('.ship-text').hide();
			$('.loading').show();
			$('#shipmentForm button').prop('disabled', true);
			
			$.ajax({
				url: 'api/shipments/create.php',
				method: 'POST',
				data: JSON.stringify(data),
				contentType: 'application/json',
				success: function(response) {
					if (response.success) {
						showAlert('Shipment created successfully! Tracking number: ' + response.data.shipment.tracking_number, 'success');
						setTimeout(function() {
							window.location.href = 'tracking.html?tracking=' + response.data.shipment.tracking_number;
						}, 3000);
					}
				},
				error: function(xhr) {
					const response = JSON.parse(xhr.responseText);
					showAlert(response.message || 'Failed to create shipment. Please try again.', 'danger');
				},
				complete: function() {
					// Hide loading state
					$('.ship-text').show();
					$('.loading').hide();
					$('#shipmentForm button').prop('disabled', false);
				}
			});
		}
		
		function showAlert(message, type) {
			const alertHtml = `
				<div class="alert alert-${type} alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert">
						<span>&times;</span>
					</button>
					${message}
				</div>
			`;
			$('#alert-container').html(alertHtml);
		}
		
		// Initialize cost calculation
		calculateCost();
	</script>

	</body>
</html>
