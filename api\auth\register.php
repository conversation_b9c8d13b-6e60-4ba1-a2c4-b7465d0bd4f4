<?php
/**
 * User Registration API
 * Handles new user account creation
 */

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST; // Fallback to form data
    }
    
    // Validate required fields
    $requiredFields = ['first_name', 'last_name', 'email', 'phone', 'password', 'address', 'city', 'postal_code'];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            ApiResponse::error("Field '$field' is required");
        }
    }
    
    // Sanitize input data
    $firstName = Database::sanitize($input['first_name']);
    $lastName = Database::sanitize($input['last_name']);
    $email = Database::sanitize($input['email']);
    $phone = Database::sanitize($input['phone']);
    $password = $input['password']; // Don't sanitize password
    $address = Database::sanitize($input['address']);
    $city = Database::sanitize($input['city']);
    $postalCode = Database::sanitize($input['postal_code']);
    $accountType = Database::sanitize($input['account_type'] ?? 'personal');
    
    // Optional fields
    $companyName = Database::sanitize($input['company_name'] ?? '');
    $registrationNumber = Database::sanitize($input['registration_number'] ?? '');
    $taxId = Database::sanitize($input['tax_id'] ?? '');
    $industry = Database::sanitize($input['industry'] ?? '');
    $newsletter = isset($input['newsletter']) ? (bool)$input['newsletter'] : false;
    
    // Validate data
    if (!Database::validateEmail($email)) {
        ApiResponse::error('Invalid email format');
    }
    
    if (!Database::validatePhone($phone)) {
        ApiResponse::error('Invalid phone number format');
    }
    
    if (strlen($password) < 8) {
        ApiResponse::error('Password must be at least 8 characters long');
    }
    
    if (strlen($firstName) < 2 || strlen($lastName) < 2) {
        ApiResponse::error('First name and last name must be at least 2 characters long');
    }
    
    // Password strength validation
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $password)) {
        ApiResponse::error('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
    }
    
    // Initialize database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check if email already exists
    $emailCheckQuery = "SELECT UserID FROM Users WHERE Email = ?";
    $emailStmt = $database->execute($emailCheckQuery, [$email]);
    
    if ($emailStmt->rowCount() > 0) {
        ApiResponse::error('Email address already exists', 409);
    }
    
    // Check if phone already exists
    $phoneCheckQuery = "SELECT UserID FROM Users WHERE Phone = ?";
    $phoneStmt = $database->execute($phoneCheckQuery, [$phone]);
    
    if ($phoneStmt->rowCount() > 0) {
        ApiResponse::error('Phone number already exists', 409);
    }
    
    // Generate username from email
    $username = strtolower(explode('@', $email)[0]);
    $originalUsername = $username;
    $counter = 1;
    
    // Ensure username is unique
    while (true) {
        $usernameCheckQuery = "SELECT UserID FROM Users WHERE Username = ?";
        $usernameStmt = $database->execute($usernameCheckQuery, [$username]);
        
        if ($usernameStmt->rowCount() == 0) {
            break; // Username is unique
        }
        
        $username = $originalUsername . $counter;
        $counter++;
        
        if ($counter > 100) {
            ApiResponse::serverError('Unable to generate unique username');
        }
    }
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Generate verification token
    $verificationToken = bin2hex(random_bytes(32));
    
    // Begin transaction
    $database->beginTransaction();
    
    try {
        // Insert user
        $userQuery = "INSERT INTO Users (
            Username, Email, PasswordHash, FirstName, LastName, Phone, Role, IsActive, 
            EmailVerified, EmailVerificationToken, CreatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, 'Customer', 0, 0, ?, NOW())";
        
        $userParams = [
            $username, $email, $hashedPassword, $firstName, $lastName, $phone, $verificationToken
        ];
        
        $database->execute($userQuery, $userParams);
        $userID = $database->lastInsertId();
        
        // Create customer record
        $customerQuery = "INSERT INTO Customer (
            FirstName, LastName, Email, Phone, Address, City, State, PostalCode, Country, IsActive
        ) VALUES (?, ?, ?, ?, ?, ?, 'Lusaka', ?, 'Zambia', 1)";
        
        $customerParams = [
            $firstName, $lastName, $email, $phone, $address, $city, $postalCode
        ];
        
        $database->execute($customerQuery, $customerParams);
        $customerID = $database->lastInsertId();
        
        // Link user to customer
        $linkQuery = "UPDATE Users SET CustomerID = ? WHERE UserID = ?";
        $database->execute($linkQuery, [$customerID, $userID]);
        
        // Create company record if business account
        $companyID = null;
        if ($accountType === 'business' && !empty($companyName)) {
            $companyQuery = "INSERT INTO Company (
                CustomerID, CompanyName, RegistrationNumber, TaxID, Industry,
                BillingAddress, ContactPerson, ContactEmail, ContactPhone
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $companyParams = [
                $customerID, $companyName, $registrationNumber, $taxId, $industry,
                $address, $firstName . ' ' . $lastName, $email, $phone
            ];
            
            $database->execute($companyQuery, $companyParams);
            $companyID = $database->lastInsertId();
        }
        
        // Create welcome notification
        $notificationQuery = "INSERT INTO Notifications (UserID, Type, Title, Message, Priority)
                             VALUES (?, 'Welcome', 'Welcome to ZamSend!', ?, 'Medium')";
        
        $welcomeMessage = "Welcome to ZamSend Courier Services! Your account has been created successfully. Please verify your email address to start shipping.";
        $database->execute($notificationQuery, [$userID, $welcomeMessage]);
        
        // Subscribe to newsletter if requested
        if ($newsletter) {
            $newsletterQuery = "INSERT INTO NewsletterSubscriptions (Email, FirstName, LastName, IsActive, SubscribedAt)
                               VALUES (?, ?, ?, 1, NOW())";
            $database->execute($newsletterQuery, [$email, $firstName, $lastName]);
        }
        
        // Log analytics
        $database->logAnalytics($userID, 'USER_REGISTRATION', 'Auth', [
            'account_type' => $accountType,
            'has_company' => !empty($companyName),
            'newsletter_subscription' => $newsletter,
            'registration_method' => 'web'
        ]);
        
        // Commit transaction
        $database->commit();
        
        // Send verification email (simulated)
        $verificationLink = "http://localhost/ZamShipment/verify-email.html?token=" . $verificationToken;
        
        // In a real application, you would send an actual email here
        // For demo purposes, we'll just log it
        error_log("Verification email would be sent to: $email with link: $verificationLink");
        
        // Prepare response data
        $responseData = [
            'user' => [
                'id' => $userID,
                'username' => $username,
                'email' => $email,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'account_type' => $accountType,
                'verification_required' => true
            ],
            'customer' => [
                'id' => $customerID,
                'company_id' => $companyID
            ],
            'verification' => [
                'email_sent' => true,
                'verification_link' => $verificationLink // Only for demo
            ]
        ];
        
        ApiResponse::success($responseData, 'Account created successfully. Please check your email for verification.', 201);
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("User registration error: " . $e->getMessage());
    
    if (isset($database)) {
        $database->logError(
            null,
            'USER_REGISTRATION_ERROR',
            $e->getMessage(),
            $e->getTraceAsString(),
            $input ?? []
        );
    }
    
    ApiResponse::serverError('An error occurred during registration. Please try again.');
}
?>
